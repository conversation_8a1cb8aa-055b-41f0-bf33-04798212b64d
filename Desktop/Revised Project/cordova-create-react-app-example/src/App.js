import React, { memo } from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import { ThemeProvider } from "@mui/material/styles";
import { CssBaseline, Box } from "@mui/material";
import Login from "./Engine/Controller/UserService/LoginAndOut/Login";
import SignUp from "./Engine/Controller/UserService/SignUp/SignUp";
import Dashboard from "./Engine/View/Dashboard/Dashboard";
import UserProfile from "./Engine/View/Pages/User/UserProfile";
import Cart from "./Engine/View/Pages/Cart/Cart";
import Home from "./Engine/View/Pages/Home/Home";
import Orders from "./Engine/View/Pages/Orders/Orders";
import ForgotPassword from "./Engine/Config/ForgotPassword";
import { theme } from "./Engine/Styles/MaterialUIs/LoginStyles";
import { Navbar } from "./Engine/components/Navbar";
import PlaceAdView from "./Engine/View/PlaceAd/PlaceAdView";
import Ads from "./Engine/View/Pages/Ads/Ads";
import AdDetailsView from "./Engine/View/Pages/AdDetails/AdDetailsView";
import CategoriesPage from "./Engine/View/Pages/Categories/CategoriesPage";
import SellerProfile from "./Engine/View/Pages/Seller/SellerProfile";

// Payment and Checkout components
import CheckoutView from "./Engine/View/Pages/Checkout/CheckoutView";
import PaymentSuccessView from "./Engine/View/Pages/PaymentSuccess/PaymentSuccessView";
import PaymentCancelView from "./Engine/View/Pages/PaymentCancel/PaymentCancelView";

// Test components
import CategoryTest from "./Engine/View/Pages/Test/CategoryTest";

// Admin imports
import AdminLogin from "./Engine/View/Pages/Admin/AdminLogin";
import AdminDashboard from "./Engine/View/Pages/Admin/AdminDashboard";
import CategoryManagement from "./Engine/View/Pages/Admin/CategoryManagement";
import AdminRouteGuard from "./Engine/View/Components/Admin/AdminRouteGuard";

const App = () => {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box className="content-container">
        <Navbar />
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<SignUp />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/profile" element={<UserProfile />} />
          <Route path="/cart" element={<Cart />} />
          <Route path="/home" element={<Home />} />
          <Route path="/orders" element={<Orders />} />
          <Route path="/ads" element={<Ads />} />
          <Route path="/place-ad" element={<PlaceAdView />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/ad/:adId" element={<AdDetailsView />} />
          <Route path="/seller/:sellerId" element={<SellerProfile />} />
          <Route path="/categoriescomponent" element={<CategoriesPage />} />

          {/* Payment Routes */}
          <Route path="/checkout" element={<CheckoutView />} />
          <Route path="/payment/success" element={<PaymentSuccessView />} />
          <Route path="/payment/cancel" element={<PaymentCancelView />} />

          {/* Test Routes */}
          {/* <Route path="/test/category" element={<CategoryTest />} /> */}

          {/* Admin Routes */}
          <Route path="/admin/login" element={<AdminLogin />} />
          <Route
            path="/admin/dashboard"
            element={
              <AdminRouteGuard>
                <AdminDashboard />
              </AdminRouteGuard>
            }
          />
          <Route
            path="/admin/categories"
            element={
              <AdminRouteGuard>
                <CategoryManagement />
              </AdminRouteGuard>
            }
          />

          <Route path="/" element={<Home />} />
        </Routes>
      </Box>
    </ThemeProvider>
  );
};

export default memo(App);

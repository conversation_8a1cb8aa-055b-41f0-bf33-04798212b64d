import {
  Card,
  CardMedia,
  Typography,
  IconButton,
  CardActionArea,
  useMediaQuery,
  useTheme,
  Snackbar,
  Alert,
  Tooltip,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import EmailIcon from "@mui/icons-material/Email";
import {
  formatPriceWithSymbol,
  calculateFinalPrice,
} from "../../Utils/CurrencyUtils";
import { addToCart } from "../../Controller/Cart/CartController";
import { useState } from "react";
import "./AdCard.css";
import "./AdCardOverrides.css"; // Import overrides to ensure styles are applied

// Helper function to check if an item is out of stock
const isOutOfStock = (ad) => {
  // Check stockStatus first - this is the most reliable indicator
  if (ad.stockStatus === "Out of Stock") {
    return true;
  }

  // Check quantity field directly (this is the primary field used in the database)
  if (typeof ad.quantity === "number" || typeof ad.quantity === "string") {
    const quantity = Number(ad.quantity);
    if (quantity === 0 || isNaN(quantity)) {
      return true;
    }
  }

  // Also check quantityInStock as a backup
  if (
    typeof ad.quantityInStock === "number" ||
    typeof ad.quantityInStock === "string"
  ) {
    const quantity = Number(ad.quantityInStock);
    if (quantity === 0 || isNaN(quantity)) {
      return true;
    }
  }

  // If we get here, the item is in stock
  return false;
};

// Helper function to get the quantity of an item
const getQuantity = (ad) => {
  // If stockStatus is "Out of Stock", always return 0 regardless of other fields
  if (ad.stockStatus === "Out of Stock") {
    return 0;
  }

  // Check quantity field directly (this is the primary field used in the database)
  if (typeof ad.quantity === "number" || typeof ad.quantity === "string") {
    const quantity = Number(ad.quantity);
    if (!isNaN(quantity)) {
      return quantity;
    }
  }

  // Also check quantityInStock as a backup
  if (
    typeof ad.quantityInStock === "number" ||
    typeof ad.quantityInStock === "string"
  ) {
    const quantity = Number(ad.quantityInStock);
    if (!isNaN(quantity)) {
      return quantity;
    }
  }

  // Default to 0 if no valid quantity found
  return 0;
};

/**
 * Compact AdCard component to display an individual ad
 * @param {Object} props - Component props
 * @param {Object} props.ad - Ad data
 */
const AdCard = ({ ad }) => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  // Get cover photo or first photo
  const getPhotoUrl = () => {
    if (!ad || !ad.photos)
      return "https://placehold.co/300x200/gray/white?text=No+Image";

    try {
      // If photos is an array
      if (Array.isArray(ad.photos)) {
        if (ad.photos.length > 0) {
          // Try to find cover photo first
          const cover = ad.photos.find((photo) => photo && photo.isCover);
          if (cover) return cover.url || cover;

          // Otherwise use first photo
          return typeof ad.photos[0] === "string"
            ? ad.photos[0]
            : ad.photos[0].url || ad.photos[0];
        }
      }
      // If photos is an object with keys
      else if (typeof ad.photos === "object") {
        const keys = Object.keys(ad.photos);
        if (keys.length > 0) {
          const firstPhoto = ad.photos[keys[0]];
          return typeof firstPhoto === "string"
            ? firstPhoto
            : firstPhoto.url || firstPhoto;
        }
      }
    } catch (error) {
      console.error("Error getting photo URL:", error);
    }

    return "https://placehold.co/300x200/gray/white?text=No+Image";
  };

  // Calculate final price with discount
  const finalPrice = calculateFinalPrice(ad.price, ad.discount);

  // Format price with Euro currency
  const formatPrice = (price) => {
    return formatPriceWithSymbol(price, "EUR");
  };

  // Handle click on ad card
  const handleAdClick = () => {
    navigate(`/ad/${ad.adId}`, { state: { adData: ad } });
  };

  // Handle right-click context menu
  const handleContextMenu = (e) => {
    // Allow default browser context menu behavior
    // This will show "Open link in new tab/window" options
    e.stopPropagation();
  };

  // State for notification
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");

  // State for notification messages

  // Handle contact seller
  const handleContactSeller = (e) => {
    e.stopPropagation(); // Prevent triggering the card click

    // Show message about contacting seller
    setSnackbarMessage(
      "Contact the seller for more information about this item"
    );
    setSnackbarSeverity("info");
    setSnackbarOpen(true);

    // Navigate to ad details page where they can contact the seller
    setTimeout(() => {
      navigate(`/ad/${ad.adId || ad.id}`);
    }, 1500);
  };

  // Handle add to cart (complete implementation)
  const handleAddToCart = (e) => {
    e.stopPropagation(); // Prevent triggering the card click

    // Check if item is out of stock using our helper function
    if (isOutOfStock(ad)) {
      setSnackbarMessage("This item is out of stock");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
      return;
    }

    // Prepare item for cart with final price and ensure currency is EUR
    const cartItem = {
      ...ad,
      finalPrice: finalPrice,
      currency: "EUR", // Always use EUR currency
      quantity: 1, // Explicitly set quantity to 1
    };

    // Add to cart
    const result = addToCart(cartItem);

    // Show notification
    if (result.success) {
      setSnackbarMessage("Item added to cart");
      setSnackbarSeverity("success");
    } else {
      setSnackbarMessage(result.message || "Failed to add item to cart");
      setSnackbarSeverity("error");
    }

    setSnackbarOpen(true);
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Truncate text to specified length
  const truncateText = (text, maxLength) => {
    if (!text) return "";
    return text.length > maxLength
      ? text.substring(0, maxLength) + "..."
      : text;
  };

  return (
    <>
      <Card
        className="ad-card"
        style={{
          width: isMobile ? "100%" : "200px",
          height: isMobile ? "220px" : "260px",
          maxWidth: "100%" /* Prevent overflow */,
          overflow: "hidden" /* Prevent overflow */,
        }}
      >
        <CardActionArea
          component="a"
          href={`/ad/${ad.adId}`}
          onClick={(e) => {
            e.preventDefault();
            handleAdClick();
          }}
          onContextMenu={handleContextMenu}
          style={{
            height: "100%",
            display: "flex",
            flexDirection: "column",
            textDecoration: "none",
            color: "inherit",
          }}
        >
          <CardMedia
            component="img"
            style={{
              height: isMobile ? "110px" : "160px",
              width: "100%",
              objectFit: "cover",
            }}
            image={getPhotoUrl()}
            alt={ad.brandName || ad.title || "Product"}
            onError={(e) => {
              e.target.src =
                "https://placehold.co/300x200/gray/white?text=No+Image";
            }}
          />
          <div
            style={{
              padding: isMobile ? "8px 0" : "10px 0",
              flex: 1,
              display: "flex",
              flexDirection: "column",
              justifyContent: "space-between",
            }}
          >
            {/* Title with padding */}
            <div style={{ padding: "0 12px" }}>
              <Typography
                variant="subtitle2"
                style={{ fontWeight: 500, lineHeight: 1.2 }}
              >
                {truncateText(ad.brandName, 30)}
              </Typography>

              {/* Stock Status */}
              <Typography
                variant="caption"
                style={{
                  color: isOutOfStock(ad)
                    ? "#d32f2f" // Red for out of stock
                    : "#2e7d32", // Green for in stock
                  fontWeight: isOutOfStock(ad) ? 700 : 500, // Make Out of Stock bolder
                  display: "block",
                  marginTop: 2,
                  backgroundColor: isOutOfStock(ad) ? "#ffebee" : "transparent", // Light red background for Out of Stock
                  padding: isOutOfStock(ad) ? "2px 4px" : 0,
                  borderRadius: isOutOfStock(ad) ? "4px" : 0,
                }}
              >
                {isOutOfStock(ad)
                  ? "OUT OF STOCK" // All caps for emphasis
                  : ad.stockStatus || "In Stock"}
                {!isOutOfStock(ad) &&
                  getQuantity(ad) <= 5 &&
                  ` (Only ${getQuantity(ad)} left)`}
              </Typography>
            </div>

            {/* Price and cart container */}
            <div
              style={{
                display: "flex",
                justifyContent:
                  "flex-start" /* Changed from space-between to flex-start */,
                alignItems: "center",
                padding: "0 12px",
                marginTop: "auto",
                overflow: "hidden" /* Prevent overflow */,
              }}
            >
              {/* Price section */}
              <div
                style={{
                  maxWidth: isMobile ? "60%" : "70%",
                  overflow: "hidden",
                }}
              >
                {ad.discount > 0 && (
                  <Typography
                    variant="caption"
                    style={{
                      textDecoration: "line-through",
                      color: "#757575",
                      display: "block",
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }}
                  >
                    {formatPrice(ad.price)}
                  </Typography>
                )}
                <Typography
                  variant="body1"
                  style={{
                    fontWeight: 600,
                    color: "#414242",
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                  }}
                >
                  {finalPrice === 0 || ad.isFreeItem
                    ? "Free Item"
                    : formatPrice(finalPrice)}
                </Typography>
                {/* Show Free Shipping if applicable */}
                {(ad.shippingMethod === "Free Shipping" ||
                  ad.shippingPayer === "seller") && (
                  <Typography
                    variant="caption"
                    style={{
                      color: "#4caf50",
                      fontWeight: 500,
                      display: "block",
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }}
                  >
                    Free Shipping
                  </Typography>
                )}
              </div>

              {/* Cart button with margin */}
              <div style={{ marginLeft: isMobile ? "20px" : "90px" }}>
                {isOutOfStock(ad) ? (
                  <Tooltip
                    title="This item is out of stock. Contact the seller for inquiries."
                    arrow
                  >
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        handleContactSeller(e);
                      }}
                    >
                      <IconButton
                        size="small"
                        aria-label="contact seller about out of stock item"
                        style={{
                          padding: "4px",
                        }}
                      >
                        <EmailIcon
                          fontSize="small"
                          style={{ color: "#d32f2f" }}
                        />
                      </IconButton>
                    </div>
                  </Tooltip>
                ) : (
                  <Tooltip title="Add to cart" arrow>
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAddToCart(e);
                      }}
                    >
                      <IconButton
                        size="small"
                        aria-label="add to cart"
                        style={{
                          padding: "4px",
                        }}
                      >
                        <ShoppingCartIcon
                          fontSize="small"
                          style={{ color: "#1976d2" }}
                        />
                      </IconButton>
                    </div>
                  </Tooltip>
                )}
              </div>
            </div>
          </div>
        </CardActionArea>
      </Card>

      {/* Notification Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          variant="filled"
          sx={{ width: "100%" }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

export default AdCard;

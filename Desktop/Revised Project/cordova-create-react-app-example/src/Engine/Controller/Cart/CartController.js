// Import dependencies
import { formatPrice } from "../../Utils/CurrencyUtils";
import { auth } from "../../Config/firebase";
import { checkAvailableQuantity } from "../../Services/InventoryService";

// Cart items are stored in localStorage
const CART_ITEMS_KEY = "cartItems";

export const addToCart = (item) => {
  try {
    if (!item || !item.adId) {
      return { success: false, message: "Invalid item data" };
    }

    // Check if user is logged in
    const currentUser = auth.currentUser;
    if (!currentUser) {
      return {
        success: false,
        message: "Please log in to add items to your cart",
        requiresLogin: true,
      };
    }

    // Get current cart items
    const currentCart = getCartItems();

    // Check if item already exists in cart
    const existingItemIndex = currentCart.findIndex(
      (cartItem) => cartItem.adId === item.adId
    );

    // Always add just 1 unit by default, regardless of the item's quantity
    // This prevents adding all available stock when clicking "Add to Cart"
    const quantityToAdd = 1;

    if (existingItemIndex >= 0) {
      // Update quantity if item already exists
      currentCart[existingItemIndex].quantity =
        (currentCart[existingItemIndex].quantity || 1) + quantityToAdd;

      // Mark that this quantity needs verification
      currentCart[existingItemIndex].quantityVerified = false;

      // Ensure userId is set (for backend compatibility)
      if (item.userId && !currentCart[existingItemIndex].userId) {
        currentCart[existingItemIndex].userId = item.userId;
      }

      // Ensure sellerId is set
      if (item.sellerId && !currentCart[existingItemIndex].sellerId) {
        currentCart[existingItemIndex].sellerId = item.sellerId;
      }
    } else {
      // Add new item with quantity 1
      currentCart.push({
        ...item,
        quantity: quantityToAdd,
        quantityVerified: false,
        addedAt: new Date().toISOString(),
        // Ensure userId is set to sellerId if not already present
        userId: item.userId || item.sellerId,
      });
    }

    // Verify the quantity asynchronously
    setTimeout(async () => {
      try {
        // Find the item again in case the cart has changed
        const updatedCart = getCartItems();
        const itemIndex = updatedCart.findIndex(
          (cartItem) => cartItem.adId === item.adId
        );

        if (itemIndex >= 0) {
          // Verify the quantity with the database
          await updateCartItemQuantity(
            item.adId,
            updatedCart[itemIndex].quantity,
            false // Don't bypass the check
          );
        }
      } catch (error) {
        console.error("Error verifying quantity:", error);
      }
    }, 100);

    // Save updated cart
    localStorage.setItem(CART_ITEMS_KEY, JSON.stringify(currentCart));

    // Notify any listeners that cart has been updated
    document.dispatchEvent(
      new CustomEvent("cartUpdated", {
        detail: { cartItems: currentCart },
      })
    );

    return {
      success: true,
      message: "Item added to cart",
      cartItems: currentCart,
      cartCount: getCartCount(),
    };
  } catch (error) {
    console.error("Error adding item to cart:", error);
    return { success: false, message: "Failed to add item to cart" };
  }
};
export const removeFromCart = (adId) => {
  try {
    if (!adId) {
      return { success: false, message: "Invalid item ID" };
    }

    // Get current cart items
    const currentCart = getCartItems();

    // Filter out the item to remove
    const updatedCart = currentCart.filter((item) => item.adId !== adId);

    // Save updated cart
    localStorage.setItem(CART_ITEMS_KEY, JSON.stringify(updatedCart));

    // Notify any listeners that cart has been updated
    document.dispatchEvent(
      new CustomEvent("cartUpdated", {
        detail: {
          cartItems: updatedCart,
          preserveSelections: true, // Flag to preserve selections in the UI
        },
      })
    );

    return {
      success: true,
      message: "Item removed from cart",
      cartItems: updatedCart,
      cartCount: getCartCount(),
    };
  } catch (error) {
    console.error("Error removing item from cart:", error);
    return { success: false, message: "Failed to remove item from cart" };
  }
};

/**
 * Update cart item quantity with database check
 * @param {string} adId - The ID of the ad to update
 * @param {number} quantity - The new quantity
 * @param {boolean} bypassCheck - Whether to bypass the database check (optional)
 * @returns {Promise<Object>} - Result object with success status and message
 */
export const updateCartItemQuantity = async (
  adId,
  quantity,
  bypassCheck = false
) => {
  try {
    if (!adId) {
      return { success: false, message: "Invalid item ID" };
    }

    if (quantity <= 0) {
      return removeFromCart(adId);
    }

    // Get current cart items
    const currentCart = getCartItems();

    // Find the item to update
    const itemIndex = currentCart.findIndex((item) => item.adId === adId);

    if (itemIndex === -1) {
      return { success: false, message: "Item not found in cart" };
    }

    // Check available quantity in the database if not bypassing
    if (!bypassCheck) {
      try {
        const result = await checkAvailableQuantity(adId);

        if (result.success) {
          const availableQuantity = result.quantity || 0;

          // If requested quantity exceeds available quantity, adjust it
          if (quantity > availableQuantity) {
            if (availableQuantity <= 0) {
              // If item is out of stock, remove it from cart
              return {
                ...removeFromCart(adId),
                message:
                  "Item is out of stock and has been removed from your cart",
                outOfStock: true,
              };
            } else {
              // Adjust to maximum available quantity
              quantity = availableQuantity;

              // Continue with the update using the adjusted quantity
              console.log(
                `Adjusting quantity to available stock: ${availableQuantity}`
              );
            }
          }
        } else {
          console.warn(
            `Could not verify quantity for ${adId}: ${result.message}`
          );
          // Continue with the update even if we couldn't verify the quantity
        }
      } catch (checkError) {
        console.error("Error checking available quantity:", checkError);
        // Continue with the update even if the check failed
      }
    }

    // Update quantity and store the adjusted quantity
    currentCart[itemIndex].quantity = quantity;

    // Store the fact that this quantity was verified with the database
    currentCart[itemIndex].quantityVerified = true;
    currentCart[itemIndex].lastVerified = Date.now();

    // Save updated cart
    localStorage.setItem(CART_ITEMS_KEY, JSON.stringify(currentCart));

    // Notify any listeners that cart has been updated
    // Include preserveSelections flag to indicate this is a quantity change
    document.dispatchEvent(
      new CustomEvent("cartUpdated", {
        detail: {
          cartItems: currentCart,
          preserveSelections: true, // Flag to preserve selections in the UI
        },
      })
    );

    return {
      success: true,
      message: "Cart updated",
      cartItems: currentCart,
      cartCount: getCartCount(),
      adjustedQuantity: quantity,
    };
  } catch (error) {
    console.error("Error updating cart item quantity:", error);
    return { success: false, message: "Failed to update cart" };
  }
};

/**
 * Get all items in the cart
 * @returns {Array} Array of cart items
 */
export const getCartItems = () => {
  try {
    const cartItemsStr = localStorage.getItem(CART_ITEMS_KEY);
    return cartItemsStr ? JSON.parse(cartItemsStr) : [];
  } catch (error) {
    console.error("Error getting cart items:", error);
    return [];
  }
};

/**
 * Get the total number of items in the cart
 * @returns {number} Total number of items
 */
export const getCartCount = () => {
  try {
    const cartItems = getCartItems();
    return cartItems.reduce((total, item) => total + (item.quantity || 1), 0);
  } catch (error) {
    console.error("Error getting cart count:", error);
    return 0;
  }
};

/**
 * Clear all items from the cart
 * @returns {Object} Success status
 */
export const clearCart = () => {
  try {
    localStorage.removeItem(CART_ITEMS_KEY);

    // Notify any listeners that cart has been updated
    document.dispatchEvent(
      new CustomEvent("cartUpdated", {
        detail: { cartItems: [] },
      })
    );

    return { success: true, message: "Cart cleared" };
  } catch (error) {
    console.error("Error clearing cart:", error);
    return { success: false, message: "Failed to clear cart" };
  }
};

/**
 * Calculate the total price of all items in the cart
 * @returns {number} Total price
 */
export const getCartTotal = () => {
  try {
    const cartItems = getCartItems();
    return cartItems.reduce((total, item) => {
      const itemPrice = parseFloat(item.finalPrice || item.price) || 0;
      return total + itemPrice * (item.quantity || 1);
    }, 0);
  } catch (error) {
    console.error("Error calculating cart total:", error);
    return 0;
  }
};

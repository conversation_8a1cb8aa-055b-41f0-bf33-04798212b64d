/**
 * OrdersController.js
 * Controller for fetching and managing user orders
 */
import { auth, database } from "../../Config/firebase";
import {
  formatPriceWithSymbol,
  DEFAULT_CURRENCY_CODE,
} from "../../Utils/CurrencyUtils";

/**
 * Generate a user-friendly order ID from a Firebase ID and timestamp
 * @param {string} firebaseId - The Firebase database ID
 * @param {number} timestamp - The order timestamp
 * @returns {string} A formatted order ID like "ORD-12345"
 */
const generateFriendlyOrderId = (firebaseId, timestamp) => {
  // Use the first 5 characters of the Firebase ID
  const shortId = firebaseId.substring(0, 5).toUpperCase();

  // Use the last 5 digits of the timestamp
  const timeDigits = String(timestamp).slice(-5);

  // Combine them to create a user-friendly ID
  return `ORD-${shortId}${timeDigits}`;
};

/**
 * Get all orders for the current user
 * @returns {Promise<Object>} Object containing orders and success status
 */
export const getUserOrders = async () => {
  try {
    const currentUser = auth.currentUser;

    if (!currentUser) {
      return { success: false, message: "User not authenticated" };
    }

    const userId = currentUser.uid;

    // Get all orders for the user
    const snapshot = await database.ref(`users/${userId}/orders`).once("value");
    const ordersData = snapshot.val() || {};

    // Convert to array and add IDs
    const orders = Object.keys(ordersData).map((orderId) => {
      const orderData = ordersData[orderId];
      const timestamp = orderData.orderDate || Date.now();

      return {
        ...orderData,
        id: orderId, // Keep the original ID for database operations
        friendlyOrderId: generateFriendlyOrderId(orderId, timestamp),
        // Format date for display
        formattedDate: new Date(timestamp).toLocaleDateString(),
        // Format total for display
        formattedTotal: formatPriceWithSymbol(
          orderData.totalPaid || orderData.finalPrice || orderData.price,
          orderData.currency || DEFAULT_CURRENCY_CODE
        ),
      };
    });

    // Sort by date (newest first)
    orders.sort((a, b) => b.orderDate - a.orderDate);

    return { success: true, orders };
  } catch (error) {
    console.error("Error fetching user orders:", error);
    return {
      success: false,
      message: error.message || "Failed to fetch orders",
    };
  }
};

/**
 * Get a specific order by ID
 * @param {string} orderId - The ID of the order to fetch
 * @returns {Promise<Object>} Object containing order data and success status
 */
export const getOrderById = async (orderId) => {
  try {
    const currentUser = auth.currentUser;

    if (!currentUser) {
      return { success: false, message: "User not authenticated" };
    }

    const userId = currentUser.uid;

    // Get the order from Firebase
    const snapshot = await database
      .ref(`users/${userId}/orders/${orderId}`)
      .once("value");
    const orderData = snapshot.val();

    if (!orderData) {
      return { success: false, message: "Order not found" };
    }

    const timestamp = orderData.orderDate || Date.now();

    return {
      success: true,
      order: {
        ...orderData,
        id: orderId,
        friendlyOrderId: generateFriendlyOrderId(orderId, timestamp),
        formattedDate: new Date(timestamp).toLocaleDateString(),
        formattedTotal: formatPriceWithSymbol(
          orderData.totalPaid || orderData.finalPrice || orderData.price,
          orderData.currency || DEFAULT_CURRENCY_CODE
        ),
      },
    };
  } catch (error) {
    console.error("Error fetching order:", error);
    return {
      success: false,
      message: error.message || "Failed to fetch order",
    };
  }
};

/**
 * Delete a specific order by ID
 * @param {string} orderId - The ID of the order to delete
 * @returns {Promise<Object>} Object containing success status and message
 */
export const deleteOrder = async (orderId) => {
  try {
    const currentUser = auth.currentUser;

    if (!currentUser) {
      return { success: false, message: "User not authenticated" };
    }

    const userId = currentUser.uid;

    // Delete the order from Firebase
    await database.ref(`users/${userId}/orders/${orderId}`).remove();

    return {
      success: true,
      message: "Order deleted successfully",
    };
  } catch (error) {
    console.error("Error deleting order:", error);
    return {
      success: false,
      message: error.message || "Failed to delete order",
    };
  }
};

/**
 * Delete all orders for the current user
 * @returns {Promise<Object>} Object containing success status and message
 */
export const deleteAllOrders = async () => {
  try {
    const currentUser = auth.currentUser;

    if (!currentUser) {
      return { success: false, message: "User not authenticated" };
    }

    const userId = currentUser.uid;

    // Delete all orders from Firebase
    await database.ref(`users/${userId}/orders`).remove();

    return {
      success: true,
      message: "All orders deleted successfully",
    };
  } catch (error) {
    console.error("Error deleting all orders:", error);
    return {
      success: false,
      message: error.message || "Failed to delete all orders",
    };
  }
};

const buyAgain = async (req, res) => {
  try {
    const { originalOrderId, userId, itemId, quantity, price, sellerId } = req.body;

    // Verify user authentication
    if (!req.user || req.user.uid !== userId) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    // Get the original order to verify it exists and belongs to the user
    const originalOrderRef = database.collection('orders').doc(originalOrderId);
    const originalOrder = await originalOrderRef.get();

    if (!originalOrder.exists) {
      return res.status(404).json({ success: false, message: 'Original order not found' });
    }

    const orderData = originalOrder.data();
    if (orderData.userId !== userId) {
      return res.status(403).json({ success: false, message: 'Not authorized to access this order' });
    }

    // Get the item details
    const itemRef = database.collection('ads').doc(itemId);
    const item = await itemRef.get();

    if (!item.exists) {
      return res.status(404).json({ success: false, message: 'Item not found' });
    }

    const itemData = item.data();
    if (!itemData.isActive) {
      return res.status(400).json({ success: false, message: 'Item is no longer available' });
    }

    // Check if item is in stock
    if (itemData.quantity < quantity) {
      return res.status(400).json({ success: false, message: 'Not enough items in stock' });
    }

    // Add item to cart
    const cartRef = database.collection('carts').doc(userId);
    const cart = await cartRef.get();

    if (!cart.exists) {
      // Create new cart
      await cartRef.set({
        items: [{
          itemId,
          quantity,
          price,
          sellerId,
          addedAt: new Date().toISOString()
        }],
        updatedAt: new Date().toISOString()
      });
    } else {
      // Update existing cart
      const cartData = cart.data();
      const existingItemIndex = cartData.items.findIndex(item => item.itemId === itemId);

      if (existingItemIndex > -1) {
        // Update quantity if item already in cart
        cartData.items[existingItemIndex].quantity += quantity;
      } else {
        // Add new item to cart
        cartData.items.push({
          itemId,
          quantity,
          price,
          sellerId,
          addedAt: new Date().toISOString()
        });
      }

      await cartRef.update({
        items: cartData.items,
        updatedAt: new Date().toISOString()
      });
    }

    res.json({
      success: true,
      message: 'Item added to cart successfully'
    });

  } catch (error) {
    console.error('Error in buyAgain:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process buy again request',
      error: error.message
    });
  }
};

import {
  fetchAllPublishedAds,
  searchPublishedAds,
} from "../Ads/PublishedAdsController";
import { database } from "../../Config/firebase";

/**
 * Search for published ads with status "published"
 * @param {string} query - The search query
 * @param {Array} categoryIds - Optional array of category IDs to filter by
 * @param {Object} filters - Additional filters (price range, make, model, discount, region)
 * @returns {Promise<Object>} - Result object with search results
 */
export const searchPublishedAdsByStatus = async (
  query,
  categoryIds = [],
  filters = {}
) => {
  try {
    console.log(`Searching for published ads with query: "${query}"`);
    console.log("Additional filters:", filters);

    // Reference to the publishedAds collection
    const adsRef = database.ref("publishedAds");

    // Get all published ads
    const snapshot = await adsRef.once("value");
    const adsData = snapshot.val() || {};

    // Convert to array and filter by status
    let ads = Object.keys(adsData)
      .map((adId) => ({
        ...adsData[adId],
        id: adId,
      }))
      .filter((ad) => ad.status === "published");

    // Apply search query if provided
    if (query && query.trim() !== "") {
      const normalizedQuery = query.toLowerCase().trim();

      ads = ads.filter((ad) => {
        // Search in title
        const titleMatch =
          ad.title && ad.title.toLowerCase().includes(normalizedQuery);

        // Search in description
        const descriptionMatch =
          ad.description &&
          ad.description.toLowerCase().includes(normalizedQuery);

        // Search in brand name
        const brandMatch =
          ad.brandName && ad.brandName.toLowerCase().includes(normalizedQuery);

        // Search in category name
        const categoryMatch =
          (ad.category &&
            typeof ad.category === "object" &&
            ad.category.name &&
            ad.category.name.toLowerCase().includes(normalizedQuery)) ||
          (ad.category &&
            typeof ad.category === "string" &&
            ad.category.toLowerCase().includes(normalizedQuery));

        // Return true if any field matches
        return titleMatch || descriptionMatch || brandMatch || categoryMatch;
      });
    }

    // Apply category filter if provided
    if (categoryIds && categoryIds.length > 0) {
      ads = ads.filter((ad) => {
        if (typeof ad.category === "object") {
          return (
            categoryIds.includes(ad.category.id) ||
            (ad.category.parentId && categoryIds.includes(ad.category.parentId))
          );
        } else {
          return (
            categoryIds.includes(ad.categoryId) ||
            (ad.parentCategoryId && categoryIds.includes(ad.parentCategoryId))
          );
        }
      });
    }

    // Apply condition filters if provided
    if (
      filters.conditions &&
      Object.values(filters.conditions).some((value) => value)
    ) {
      ads = ads.filter((ad) => {
        // If no condition filters are selected, show all ads
        if (!Object.values(filters.conditions).some((value) => value)) {
          return true;
        }

        // If no condition is specified in the ad, check if "notSpecified" is selected
        if (!ad.condition) {
          return filters.conditions.notSpecified;
        }

        const condition = ad.condition.toLowerCase();

        // Match condition with filter
        if (condition === "new" && filters.conditions.new) {
          return true;
        }
        if (
          (condition === "new other" ||
            condition === "new other (see details)") &&
          filters.conditions.newOther
        ) {
          return true;
        }
        if (
          condition === "remanufactured" &&
          filters.conditions.remanufactured
        ) {
          return true;
        }
        if (condition === "used" && filters.conditions.used) {
          return true;
        }
        if (
          (condition === "for parts" ||
            condition === "for parts or not working") &&
          filters.conditions.forParts
        ) {
          return true;
        }
        if (condition === "not specified" && filters.conditions.notSpecified) {
          return true;
        }

        return false;
      });
    }

    // Apply price range filter if provided
    if (filters.priceMin !== undefined || filters.priceMax !== undefined) {
      ads = ads.filter((ad) => {
        const price = parseFloat(ad.price) || 0;
        const priceMin =
          filters.priceMin !== undefined ? parseFloat(filters.priceMin) : 0;
        const priceMax =
          filters.priceMax !== undefined
            ? parseFloat(filters.priceMax)
            : Infinity;

        return price >= priceMin && price <= priceMax;
      });
    }

    // Apply location filter if provided
    if (filters.location && filters.location.trim() !== "") {
      const normalizedLocation = filters.location.toLowerCase().trim();
      ads = ads.filter((ad) => {
        // Check seller location if available
        if (ad.sellerInfo && ad.sellerInfo.location) {
          const location = ad.sellerInfo.location;

          // Check if location is an object with formatted location
          if (typeof location === "object") {
            // Check formatted location
            if (
              location.formattedLocation &&
              location.formattedLocation
                .toLowerCase()
                .includes(normalizedLocation)
            ) {
              return true;
            }

            // Check address, state, country
            const address = location.address || "";
            const stateName =
              location.state && location.state.name ? location.state.name : "";
            const countryName =
              location.country && location.country.name
                ? location.country.name
                : "";

            return (
              address.toLowerCase().includes(normalizedLocation) ||
              stateName.toLowerCase().includes(normalizedLocation) ||
              countryName.toLowerCase().includes(normalizedLocation)
            );
          }

          // If location is a string
          if (typeof location === "string") {
            return location.toLowerCase().includes(normalizedLocation);
          }
        }

        // If no location info, check shipping from
        if (ad.shippingFrom) {
          return ad.shippingFrom.toLowerCase().includes(normalizedLocation);
        }

        // Check city or country fields if they exist
        if (ad.city && ad.city.toLowerCase().includes(normalizedLocation)) {
          return true;
        }

        if (
          ad.country &&
          ad.country.toLowerCase().includes(normalizedLocation)
        ) {
          return true;
        }

        return false;
      });
    }

    console.log(`Found ${ads.length} published ads matching criteria`);

    return {
      success: true,
      data: {
        results: ads,
        total: ads.length,
        query,
        filters,
      },
    };
  } catch (error) {
    console.error("Error searching published ads:", error);
    return {
      success: false,
      message: error.message || "Failed to search ads",
      data: {
        results: [],
        total: 0,
      },
    };
  }
};

export const performSearch = async (
  keyword,
  onSearchComplete,
  categoryIds = [],
  filters = {}
) => {
  try {
    console.log("SearchController: performSearch called with:", {
      keyword,
      categoryIds,
      filters,
    });

    if (!keyword || keyword.trim() === "") {
      console.log("SearchController: Clearing search results");
      // Clear search results
      localStorage.removeItem("searchTerm");
      localStorage.removeItem("searchResults");
      localStorage.removeItem("originalSearchResults");
      localStorage.removeItem("searchCategoryIds");
      localStorage.removeItem("searchFilters");
      localStorage.setItem("searchTimestamp", Date.now().toString());

      if (onSearchComplete) {
        onSearchComplete("", null);
      }

      // Force a refresh of the page by dispatching a custom event
      document.dispatchEvent(new CustomEvent("storageUpdated"));
      return;
    }

    console.log("SearchController: Performing search for:", keyword);
    console.log("SearchController: Category filters:", categoryIds);
    console.log("SearchController: Additional filters:", filters);

    // Use the backend API search function
    const result = await searchPublishedAds(keyword);

    if (result.success) {
      console.log(
        "SearchController: Search results:",
        result.data.results.length,
        "results"
      );

      // Store search term, category filters, additional filters, and results in localStorage
      localStorage.setItem("searchTerm", keyword);
      localStorage.setItem(
        "searchResults",
        JSON.stringify(result.data.results)
      );
      // Store original search results for filtering
      localStorage.setItem(
        "originalSearchResults",
        JSON.stringify(result.data.results)
      );

      // Store category filters
      if (categoryIds && categoryIds.length > 0) {
        localStorage.setItem("searchCategoryIds", JSON.stringify(categoryIds));
      } else {
        localStorage.removeItem("searchCategoryIds");
      }

      // Store additional filters
      if (filters && Object.keys(filters).length > 0) {
        localStorage.setItem("searchFilters", JSON.stringify(filters));
      } else {
        localStorage.removeItem("searchFilters");
      }

      localStorage.setItem("searchTimestamp", Date.now().toString());

      // Call the callback function
      if (onSearchComplete) {
        console.log(
          "SearchController: Calling onSearchComplete with:",
          keyword,
          result.data.results.length,
          "results"
        );
        onSearchComplete(keyword, result.data.results);
      }

      // Force a refresh of the page by dispatching a custom event
      document.dispatchEvent(new CustomEvent("storageUpdated"));
    } else {
      console.error("SearchController: Search failed:", result.message);

      // Call the callback with empty results
      if (onSearchComplete) {
        onSearchComplete(keyword, []);
      }
    }
  } catch (error) {
    console.error("SearchController: Error performing search:", error);

    // Call the callback with empty results
    if (onSearchComplete) {
      onSearchComplete(keyword, []);
    }
  }
};

export const getSearchResults = () => {
  try {
    console.log("🔍 SearchController: getSearchResults called");

    const searchTerm = localStorage.getItem("searchTerm") || "";
    const searchResultsStr = localStorage.getItem("searchResults");
    const searchCategoryIdsStr = localStorage.getItem("searchCategoryIds");
    const searchFiltersStr = localStorage.getItem("searchFilters");

    // Also check filter-specific localStorage items
    const filterSort = localStorage.getItem("filterSort");
    const filterDiscount = localStorage.getItem("filterDiscount");
    const filterLocation = localStorage.getItem("filterLocation");

    console.log("🔍 SearchController: Retrieved from localStorage:", {
      searchTerm,
      hasSearchResults: !!searchResultsStr,
      searchResultsLength: searchResultsStr
        ? JSON.parse(searchResultsStr).length
        : 0,
      hasSearchCategoryIds: !!searchCategoryIdsStr,
      hasSearchFilters: !!searchFiltersStr,
      searchFilters: searchFiltersStr ? JSON.parse(searchFiltersStr) : null,
      // Filter states
      hasFilterSort: !!filterSort,
      hasFilterDiscount: !!filterDiscount,
      hasFilterLocation: !!filterLocation,
      filterSort: filterSort ? JSON.parse(filterSort) : null,
      filterDiscount: filterDiscount ? JSON.parse(filterDiscount) : null,
      filterLocation: filterLocation ? JSON.parse(filterLocation) : null,
    });

    // Check if we have search results (either from search or from filters)
    if (searchResultsStr) {
      const searchResults = JSON.parse(searchResultsStr);
      const categoryIds = searchCategoryIdsStr
        ? JSON.parse(searchCategoryIdsStr)
        : [];
      const filters = searchFiltersStr ? JSON.parse(searchFiltersStr) : {};

      console.log("🔍 SearchController: getSearchResults returning:", {
        searchTerm,
        resultsCount: searchResults.length,
        hasFilters: Object.keys(filters).length > 0,
        categoryIds,
        filters,
      });

      return { searchTerm, searchResults, categoryIds, filters };
    }
  } catch (error) {
    console.error("🔍 SearchController: Error getting search results:", error);
  }

  console.log("🔍 SearchController: getSearchResults returning empty results");
  return { searchTerm: "", searchResults: null, categoryIds: [], filters: {} };
};

/**
 * Clear search results
 */
export const clearSearchResults = () => {
  localStorage.removeItem("searchTerm");
  localStorage.removeItem("searchResults");
  localStorage.removeItem("originalSearchResults");
  localStorage.removeItem("searchCategoryIds");
  localStorage.removeItem("searchFilters");
  localStorage.setItem("searchTimestamp", Date.now().toString());
  document.dispatchEvent(new CustomEvent("storageUpdated"));
};

// Monitor localStorage changes to track when data is being cleared
const monitorLocalStorage = () => {
  const originalSetItem = localStorage.setItem;
  const originalRemoveItem = localStorage.removeItem;
  const originalClear = localStorage.clear;

  localStorage.setItem = function (key, value) {
    if (key.includes("filter") || key.includes("search")) {
      console.log(`🔍 localStorage.setItem: ${key} =`, value);
    }
    return originalSetItem.apply(this, arguments);
  };

  localStorage.removeItem = function (key) {
    if (key.includes("filter") || key.includes("search")) {
      console.log(`❌ localStorage.removeItem: ${key}`);
      console.trace("localStorage.removeItem called from:");
    }
    return originalRemoveItem.apply(this, arguments);
  };

  localStorage.clear = function () {
    console.log(`❌ localStorage.clear() called`);
    console.trace("localStorage.clear called from:");
    return originalClear.apply(this, arguments);
  };
};

export const initHomePage = ({
  onSearchResultsChange,
  onAdsLoaded,
  onError,
  onLoadingChange,
}) => {
  console.log("SearchController: Initializing home page");

  // Monitor localStorage changes
  monitorLocalStorage();

  // Start loading
  if (onLoadingChange) {
    onLoadingChange(true);
  }

  // Handle storage events (for cross-tab synchronization)
  const handleStorageChange = (e) => {
    if (
      e.key === "searchTimestamp" ||
      e.key === "searchResults" ||
      e.key === "searchTerm"
    ) {
      console.log("SearchController: Storage event detected:", e.key);
      if (onSearchResultsChange) {
        onSearchResultsChange(getSearchResults());
      }
    }
  };

  // Handle custom storageUpdated event
  const handleStorageUpdated = () => {
    console.log("🔄 SearchController: StorageUpdated event detected");
    const results = getSearchResults();
    console.log(
      "🔄 SearchController: Calling onSearchResultsChange with:",
      results
    );
    if (onSearchResultsChange) {
      onSearchResultsChange(results);
    }
  };

  // Add event listeners
  window.addEventListener("storage", handleStorageChange);
  document.addEventListener("storageUpdated", handleStorageUpdated);

  // Check for initial search results
  console.log("🚀 SearchController: Checking for initial search results");
  const initialResults = getSearchResults();
  console.log("🚀 SearchController: Initial results:", initialResults);
  if (onSearchResultsChange) {
    console.log(
      "🚀 SearchController: Calling onSearchResultsChange with initial results"
    );
    onSearchResultsChange(initialResults);
  }

  // Fetch all ads
  fetchAllAds(
    // Success callback
    (ads) => {
      if (onAdsLoaded) {
        onAdsLoaded(ads);
      }
    },
    // Error callback
    (errorMessage) => {
      if (onError) {
        onError(errorMessage);
      }
    },
    // Complete callback
    () => {
      if (onLoadingChange) {
        onLoadingChange(false);
      }
    }
  );

  // Return cleanup function
  return () => {
    console.log("SearchController: Cleaning up home page");
    window.removeEventListener("storage", handleStorageChange);
    document.removeEventListener("storageUpdated", handleStorageUpdated);
  };
};

export const initSearchListeners = (onSearchResultsChange) => {
  console.log("SearchController: Initializing search listeners only");

  // Handle storage events (for cross-tab synchronization)
  const handleStorageChange = (e) => {
    if (
      e.key === "searchTimestamp" ||
      e.key === "searchResults" ||
      e.key === "searchTerm"
    ) {
      console.log("SearchController: Storage event detected:", e.key);
      if (onSearchResultsChange) {
        onSearchResultsChange(getSearchResults());
      }
    }
  };

  // Handle custom storageUpdated event
  const handleStorageUpdated = () => {
    console.log("SearchController: StorageUpdated event detected");
    if (onSearchResultsChange) {
      onSearchResultsChange(getSearchResults());
    }
  };

  // Add event listeners
  window.addEventListener("storage", handleStorageChange);
  document.addEventListener("storageUpdated", handleStorageUpdated);

  // Check for initial search results
  if (onSearchResultsChange) {
    onSearchResultsChange(getSearchResults());
  }

  // Return cleanup function
  return () => {
    console.log("SearchController: Removing search listeners");
    window.removeEventListener("storage", handleStorageChange);
    document.removeEventListener("storageUpdated", handleStorageUpdated);
  };
};

export const fetchAllAds = async (onSuccess, onError, onComplete) => {
  try {
    console.log("SearchController: Fetching all published ads");

    // Fetch all published ads
    const result = await fetchAllPublishedAds();

    if (result.success) {
      console.log(
        "SearchController: Successfully fetched",
        result.data.published.length,
        "ads"
      );
      if (onSuccess) {
        onSuccess(result.data.published);
      }
    } else {
      console.error("SearchController: Failed to fetch ads:", result.message);
      if (onError) {
        onError(result.message);
      }
    }
  } catch (error) {
    console.error("SearchController: Error fetching ads:", error);
    if (onError) {
      onError(error.message);
    }
  } finally {
    if (onComplete) {
      onComplete();
    }
  }
};

/**
 * Fetch all categories from Firebase
 * @param {function} onSuccess - Success callback
 * @param {function} onError - Error callback
 * @returns {Promise<void>}
 */
export const fetchCategories = async (onSuccess, onError) => {
  try {
    console.log("SearchController: Fetching categories");

    // Reference to the categories collection
    const categoriesRef = database.ref("categories");

    // Get all categories
    const snapshot = await categoriesRef.once("value");
    const categoriesData = snapshot.val() || {};

    // Convert to array
    const categories = Object.keys(categoriesData).map((categoryId) => ({
      ...categoriesData[categoryId],
      id: categoryId,
    }));

    console.log(`SearchController: Fetched ${categories.length} categories`);

    // Separate into parent categories and subcategories
    const parentCategories = categories.filter(
      (category) => !category.parentId
    );

    const subcategories = categories.filter((category) => category.parentId);

    // Add subcategories to their parent categories
    parentCategories.forEach((parent) => {
      parent.subcategories = subcategories.filter(
        (sub) => sub.parentId === parent.id
      );
    });

    if (onSuccess) {
      onSuccess({
        all: categories,
        parents: parentCategories,
        subcategories: subcategories,
      });
    }

    return {
      success: true,
      categories: {
        all: categories,
        parents: parentCategories,
        subcategories: subcategories,
      },
    };
  } catch (error) {
    console.error("SearchController: Error fetching categories:", error);

    if (onError) {
      onError(error.message);
    }

    return {
      success: false,
      message: error.message || "Failed to fetch categories",
      categories: {
        all: [],
        parents: [],
        subcategories: [],
      },
    };
  }
};

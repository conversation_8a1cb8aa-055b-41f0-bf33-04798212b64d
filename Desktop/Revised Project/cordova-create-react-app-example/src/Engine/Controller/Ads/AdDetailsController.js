import { auth, database } from "../../Config/firebase";
// Import currency utilities
import {
  formatPriceWithSymbol as formatPrice,
  getCurrencySymbol,
  calculateFinalPrice,
} from "../../Utils/CurrencyUtils";

// Export the imported functions
export { formatPrice, getCurrencySymbol, calculateFinalPrice };

/**
 * Fetch ad details by ID
 * @param {string} adId - The ID of the ad to fetch
 * @returns {Promise<Object>} Success status and ad data
 */
export const getAdDetails = async (adId) => {
  try {
    console.log("Fetching ad details for:", adId);

    let adData = null;
    let sellerId = null;

    // First try to find the ad in any user's publishedAds
    const usersSnapshot = await database.ref('users').once('value');
    const users = usersSnapshot.val();
    
    if (users) {
      for (const userId in users) {
        const userPublishedAdsSnapshot = await database
          .ref(`users/${userId}/publishedAds/${adId}`)
          .once('value');
        
        if (userPublishedAdsSnapshot.exists()) {
          adData = userPublishedAdsSnapshot.val();
          sellerId = userId;
          console.log("Found ad data:", adData);
          console.log("Found seller ID:", sellerId);
          break;
        }
      }
    }

    // If not found in any user's publishedAds, check if the current user owns this ad
    if (!adData) {
      const currentUser = auth.currentUser;
      if (currentUser) {
        const userId = currentUser.uid;
        
        // Check in user's ads
        const userAdSnapshot = await database
          .ref(`users/${userId}/ads/${adId}`)
          .once("value");
        if (userAdSnapshot.exists()) {
          adData = userAdSnapshot.val();
          sellerId = userId;
          console.log("Found ad in user's ads:", adData);
        }
      }
    }

    if (!adData) {
      console.log("Ad not found in any location");
      return { success: false, message: "Ad not found" };
    }

    // Get seller information - try multiple paths
    let sellerInfo = {};
    if (sellerId) {
      // Try to get from profile
      const sellerProfileSnapshot = await database
        .ref(`users/${sellerId}/profile`)
        .once('value');
      
      // Try to get from userData
      const sellerUserDataSnapshot = await database
        .ref(`users/${sellerId}/userData`)
        .once('value');
      
      // Try to get direct user data
      const sellerDirectSnapshot = await database
        .ref(`users/${sellerId}`)
        .once('value');

      const profileData = sellerProfileSnapshot.val() || {};
      const userDataData = sellerUserDataSnapshot.val() || {};
      const directData = sellerDirectSnapshot.val() || {};

      console.log("Profile data:", profileData);
      console.log("UserData data:", userDataData);
      console.log("Direct user data:", directData);

      // Combine all seller data, prioritizing profile > userData > direct
      sellerInfo = {
        ...directData,
        ...userDataData,
        ...profileData,
        // Ensure we have these critical fields
        displayName: profileData.displayName || userDataData.displayName || directData.displayName || directData.name || 'Unknown Seller',
        email: profileData.email || userDataData.email || directData.email || 'No email provided',
        location: profileData.location || userDataData.location || directData.location || 'Location not specified',
        rating: profileData.rating || userDataData.rating || directData.rating || 0,
      };

      console.log("Combined seller info:", sellerInfo);
    }

    // Get the original ad data from the seller's profile if available
    if (sellerId && !adData.photos && !adData.images) {
      const originalAdSnapshot = await database
        .ref(`users/${sellerId}/ads/${adId}`)
        .once('value');
      const originalAdData = originalAdSnapshot.val() || {};
      adData = { ...adData, ...originalAdData };
    }

    // Combine ad data with seller info
    const enrichedAdData = {
      ...adData,
      adId: adId,
      userId: sellerId,
      sellerInfo,
      sellerName: sellerInfo.displayName,
      sellerEmail: sellerInfo.email,
      // Ensure all critical fields are present
      price: adData.price || 0,
      currency: adData.currency || 'USD',
      itemName: adData.itemName || adData.title || '',
      description: adData.description || '',
      images: adData.images || adData.photos || [],
      photos: adData.photos || adData.images || [],
      category: adData.category || '',
      condition: adData.condition || '',
      quantity: adData.quantity || 0,
      createdAt: adData.createdAt || Date.now(),
      shippingMethod: adData.shippingMethod || '',
      shippingFrom: adData.shippingFrom || '',
      estimatedDelivery: adData.estimatedDelivery || '',
      returnOption: adData.returnOption || ''
    };

    console.log("Returning enriched ad data:", enrichedAdData);
    return { success: true, data: enrichedAdData };
  } catch (error) {
    console.error("Error fetching ad details:", error);
    return { success: false, message: "Failed to fetch ad details" };
  }
};

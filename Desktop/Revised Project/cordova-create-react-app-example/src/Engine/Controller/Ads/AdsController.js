import { auth, database } from "../../Config/firebase";
import {
  updateCategoriesCollection,
  removeFromCategoriesCollection,
} from "../../Model/Category/CategoryUpdater";

/**
 * Get all ads for the current user
 * @returns {Promise<Object>} Object containing draft, published, and sold ads
 */
export const getUserAds = async () => {
  try {
    const currentUser = auth.currentUser;

    if (!currentUser) {
      return { success: false, message: "User not authenticated" };
    }

    const userId = currentUser.uid;

    // Get all ads for the user
    const snapshot = await database.ref(`users/${userId}/ads`).once("value");
    const adsData = snapshot.val() || {};

    // Separate ads into drafts and published (do not include sold here)
    const drafts = [];
    const published = [];

    Object.keys(adsData).forEach((adId) => {
      const ad = { ...adsData[adId], id: adId };
      if (ad.status === "draft") {
        drafts.push(ad);
      } else if (ad.status === "published") {
        published.push(ad);
      }
    });

    // Fetch sold items from soldItems collection
    const soldSnapshot = await database.ref(`users/${userId}/soldItems`).once("value");
    const soldData = soldSnapshot.val() || {};
    const sold = Object.keys(soldData).map((adId) => ({ ...soldData[adId], id: adId }));
    sold.sort((a, b) => (b.soldAt || 0) - (a.soldAt || 0));

    return {
      success: true,
      data: {
        drafts,
        published,
        sold,
        draftCount: drafts.length,
        publishedCount: published.length,
        soldCount: sold.length
      },
    };
  } catch (error) {
    console.error("Error fetching user ads:", error);
    return { success: false, message: error.message };
  }
};

/**
 * Delete an ad
 * @param {string} adId - The ID of the ad to delete
 * @returns {Promise<Object>} Success status and message
 */
export const deleteAd = async (adId) => {
  try {
    const currentUser = auth.currentUser;

    if (!currentUser) {
      return { success: false, message: "User not authenticated" };
    }

    const userId = currentUser.uid;
    const email = currentUser.email;

    // Get the ad data first (to get the photos for Cloudinary deletion)
    const snapshot = await database
      .ref(`users/${userId}/ads/${adId}`)
      .once("value");
    const adData = snapshot.val();

    if (!adData) {
      return { success: false, message: "Ad not found" };
    }

    // Delete photos from Cloudinary
    if (adData.photos && adData.photos.length > 0) {
      try {
        // Delete all photos in the folder
        const deleteResult = await deleteAdPhotos(
          email,
          adData.brandName,
          adId
        );
        console.log("Photos deletion result:", deleteResult);
      } catch (photoError) {
        console.error("Error deleting photos:", photoError);
        // Continue with ad deletion even if photo deletion fails
      }
    }

    // Create an array of promises for all delete operations
    const deletePromises = [];

    // Delete from main ads collection
    deletePromises.push(database.ref(`users/${userId}/ads/${adId}`).remove());

    // If the ad was published, remove from additional collections
    if (adData.status === "published") {
      // Find and delete any orders with this adId
      const ordersSnapshot = await database
        .ref(`users/${userId}/orders`)
        .orderByChild("adId")
        .equalTo(adId)
        .once("value");
      const orders = ordersSnapshot.val();

      if (orders) {
        Object.keys(orders).forEach((orderId) => {
          deletePromises.push(
            database.ref(`users/${userId}/orders/${orderId}`).remove()
          );
        });
      }

      // Remove from categories collection
      console.log("Removing deleted ad from categories collection");
      deletePromises.push(removeFromCategoriesCollection(adData));

      // Remove from publishedAds collection
      console.log("Removing deleted ad from publishedAds collection");
      deletePromises.push(database.ref(`publishedAds/${adId}`).remove());
    }

    // Execute all delete operations
    await Promise.all(deletePromises);

    return {
      success: true,
      message: "Ad deleted successfully",
    };
  } catch (error) {
    console.error("Error deleting ad:", error);
    return { success: false, message: error.message };
  }
};

/**
 * Delete photos from Cloudinary
 * @param {string} email - User email
 * @param {string} brandName - Brand name
 * @param {string} adId - Ad ID
 * @returns {Promise<Object>} Success status and message
 */
export const deleteAdPhotos = async (email, brandName, adId) => {
  try {
    console.log("Deleting all photos from Cloudinary:", {
      email,
      brandName,
      adId,
    });

    // This requires a server call to delete from Cloudinary
    const response = await fetch(
      "http://localhost:5000/api/ads/delete-photos",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          brandName,
          adId,
        }),
        credentials: "include",
      }
    );

    const result = await response.json();
    console.log("Cloudinary deletion response:", result);
    return result;
  } catch (error) {
    console.error("Error deleting photos:", error);
    return { success: false, message: error.message };
  }
};

/**
 * Delete a single photo from Cloudinary
 * @param {string} email - User email
 * @param {string} brandName - Brand name
 * @param {string} adId - Ad ID
 * @param {string} photoUrl - URL of the photo to delete
 * @returns {Promise<Object>} Success status and message
 */
export const deleteSinglePhoto = async (email, brandName, adId, photoUrl) => {
  try {
    console.log("Deleting single photo from Cloudinary:", {
      email,
      brandName,
      adId,
      photoUrl,
    });

    // Extract the filename from the URL
    const urlParts = photoUrl.split("/");
    const filenameWithExt = urlParts[urlParts.length - 1];
    const filename = filenameWithExt.split(".")[0];

    console.log("Extracted filename:", filename);

    // Make API call to delete the photo
    const response = await fetch("http://localhost:5000/api/ads/delete-photo", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email,
        brandName,
        adId,
        filename,
      }),
      credentials: "include",
    });

    const result = await response.json();
    console.log("Single photo deletion response:", result);
    return result;
  } catch (error) {
    console.error("Error deleting single photo:", error);
    return { success: false, message: error.message };
  }
};

/**
 * Change ad status (draft to published or vice versa)
 * @param {string} adId - The ID of the ad
 * @param {string} newStatus - The new status ("draft" or "published")
 * @returns {Promise<Object>} Success status and message
 */
export const changeAdStatus = async (adId, newStatus) => {
  try {
    const currentUser = auth.currentUser;

    if (!currentUser) {
      return { success: false, message: "User not authenticated" };
    }

    const userId = currentUser.uid;
    const email = currentUser.email;

    // Get the current ad data
    const snapshot = await database
      .ref(`users/${userId}/ads/${adId}`)
      .once("value");
    const adData = snapshot.val();

    if (!adData) {
      return { success: false, message: "Ad not found" };
    }

    // Get seller information
    const sellerSnapshot = await database.ref(`users/${userId}`).once("value");
    const sellerData = sellerSnapshot.val();

    // Update the status
    await database.ref(`users/${userId}/ads/${adId}`).update({
      status: newStatus,
      updatedAt: Date.now(),
    });

    // If changing to published, update categories and published ads
    if (newStatus === "published" && adData.status !== "published") {
      // Create the published ad with seller information
      const publishedAdData = {
        ...adData,
        status: newStatus,
        updatedAt: Date.now(),
        userId: userId,
        userEmail: email,
        userName: sellerData?.displayName || "Unknown User",
        sellerInfo: {
          email: email,
          displayName: sellerData?.displayName || "Unknown User",
          phoneNumber: sellerData?.phoneNumber || "Not provided",
          profilePicture: sellerData?.profilePicture || null,
          rating: sellerData?.rating || 0,
          totalSales: sellerData?.totalSales || 0,
          memberSince: sellerData?.createdAt || null,
          location: sellerData?.location || null
        }
      };

      // Save to published ads
      await database.ref(`publishedAds/${adId}`).set(publishedAdData);

      // Update the categories collection
      console.log("Updating categories collection for newly published ad");
      await updateCategoriesCollection({
        ...publishedAdData,
        adId,
      });
    } else if (newStatus === "draft" && adData.status === "published") {
      // Remove from published ads when unpublishing
      await database.ref(`publishedAds/${adId}`).remove();
    }

    return { success: true, message: `Ad ${newStatus} successfully` };
  } catch (error) {
    console.error("Error changing ad status:", error);
    return { success: false, message: error.message };
  }
};

// Update getSoldItemDetails to fetch from soldItems first, then fallback to ads
export const getSoldItemDetails = async (adId) => {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      return { success: false, message: "User not authenticated" };
    }
    const userId = currentUser.uid;
    // Try soldItems first
    let soldSnapshot = await database.ref(`users/${userId}/soldItems/${adId}`).once("value");
    let soldItemData = soldSnapshot.val();
    if (!soldItemData) {
      // Fallback to ads collection (status: sold)
      const adSnapshot = await database.ref(`users/${userId}/ads/${adId}`).once("value");
      soldItemData = adSnapshot.val();
      if (!soldItemData || soldItemData.status !== "sold") {
        return { success: false, message: "Sold item not found" };
      }
    }
    // Get additional order details from paidOrders if available
    const paidOrderSnapshot = await database.ref(`paidOrders`)
      .orderByChild("orderId")
      .equalTo(adId)
      .once("value");
    const paidOrderData = paidOrderSnapshot.val();
    if (paidOrderData) {
      const orderDetails = Object.values(paidOrderData)[0];
      soldItemData = {
        ...soldItemData,
        orderDetails: {
          orderStatus: orderDetails.orderStatus,
          paymentStatus: orderDetails.paymentStatus,
          paymentIntentId: orderDetails.paymentIntentId
        }
      };
    }
    // Get seller information
    let sellerInfo = {};
    if (soldItemData.userId) {
      try {
        const sellerSnapshot = await database.ref(`users/${soldItemData.userId}`).once("value");
        const sellerData = sellerSnapshot.val();
        if (sellerData) {
          sellerInfo = {
            email: sellerData.email || "Not provided",
            displayName: sellerData.displayName || "Not provided",
            phoneNumber: sellerData.phoneNumber || "Not provided",
            profilePicture: sellerData.profilePicture || null,
            rating: sellerData.rating || 0,
            totalSales: sellerData.totalSales || 0,
            memberSince: sellerData.createdAt || null
          };
        }
      } catch (error) {
        console.error("Error fetching seller information:", error);
      }
    }
    // Get buyer information from location collection
    let buyerInfo = {};
    if (soldItemData.soldTo) {
      try {
        const buyerLocationSnapshot = await database.ref(`users/${soldItemData.soldTo}/location`).once("value");
        const buyerLocation = buyerLocationSnapshot.val();
        if (buyerLocation) {
          buyerInfo = {
            address: buyerLocation.address || "Not provided",
            country: buyerLocation.country || "Not provided",
            formattedLocation: buyerLocation.formattedLocation || "Not provided",
            state: buyerLocation.state || "Not provided"
          };
        }
      } catch (error) {
        console.error("Error fetching buyer location:", error);
      }
    }
    // Get buyer's email from users collection
    let buyerEmail = "Unknown";
    if (soldItemData.soldTo) {
      try {
        const buyerSnapshot = await database.ref(`users/${soldItemData.soldTo}`).once("value");
        const buyerData = buyerSnapshot.val();
        if (buyerData && buyerData.email) {
          buyerEmail = buyerData.email;
        }
      } catch (error) {
        console.error("Error fetching buyer email:", error);
      }
    }
    // Ensure photos array exists and is properly formatted
    let photos = [];
    if (soldItemData.photos) {
      photos = Array.isArray(soldItemData.photos) ? soldItemData.photos : [soldItemData.photos];
    } else if (soldItemData.photoURL) {
      photos = [{ url: soldItemData.photoURL }];
    }
    return {
      success: true,
      data: {
        ...soldItemData,
        id: adId,
        status: "sold",
        buyerInfo,
        sellerInfo,
        soldToEmail: buyerEmail,
        photos: photos
      }
    };
  } catch (error) {
    console.error("Error fetching sold item details:", error);
    return { success: false, message: error.message };
  }
};

export const deleteSoldItem = async (adId) => {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      return { success: false, message: "User not authenticated" };
    }
    const userId = currentUser.uid;

    // Create an array of promises for all delete operations
    const deletePromises = [];

    // Try to delete from main ads collection, but don't fail if not found
    try {
      const adDeleteResult = await deleteAd(adId);
      if (!adDeleteResult.success) {
        console.log("Ad not found in main collection, continuing with sold item deletion");
      }
    } catch (error) {
      console.log("Error deleting from main collection, continuing with sold item deletion:", error);
    }

    // Remove from paidOrders if exists
    const paidOrderSnapshot = await database.ref(`paidOrders`)
      .orderByChild("orderId")
      .equalTo(adId)
      .once("value");
    const paidOrderData = paidOrderSnapshot.val();
    if (paidOrderData) {
      const paidOrderId = Object.keys(paidOrderData)[0];
      deletePromises.push(database.ref(`paidOrders/${paidOrderId}`).remove());
    }

    // Remove from soldItems
    deletePromises.push(database.ref(`users/${userId}/soldItems/${adId}`).remove());

    // Execute all remaining delete operations
    await Promise.all(deletePromises);

    return { success: true, message: "Sold item deleted successfully" };
  } catch (error) {
    console.error("Error deleting sold item:", error);
    return { success: false, message: error.message };
  }
};

export const republishSoldItem = async (adId) => {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      return { success: false, message: "User not authenticated" };
    }
    const userId = currentUser.uid;
    // Try soldItems first
    let ref = database.ref(`users/${userId}/soldItems/${adId}`);
    let snapshot = await ref.once("value");
    let soldItemData = snapshot.val();
    if (!soldItemData) {
      // Try ads collection (status: sold)
      ref = database.ref(`users/${userId}/ads/${adId}`);
      snapshot = await ref.once("value");
      soldItemData = snapshot.val();
      if (!soldItemData || soldItemData.status !== "sold") {
        return { success: false, message: "Sold item not found" };
      }
    }
    // Prepare new ad data (as draft, keep same adId)
    const adData = {
      ...soldItemData,
      status: "draft",
      updatedAt: Date.now(),
      soldAt: null,
      orderDetails: null,
      soldTo: null,
      soldToEmail: null
    };
    await database.ref(`users/${userId}/ads/${adId}`).set(adData);
    // Remove from soldItems if it existed there
    await database.ref(`users/${userId}/soldItems/${adId}`).remove();
    return { success: true, message: "Sold item republished as draft", adId };
  } catch (error) {
    console.error("Error republishing sold item:", error);
    return { success: false, message: error.message };
  }
};

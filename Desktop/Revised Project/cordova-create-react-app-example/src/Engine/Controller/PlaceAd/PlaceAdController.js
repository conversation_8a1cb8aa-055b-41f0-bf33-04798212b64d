import { auth, database } from "../../Config/firebase";
import axios from "axios";
import Compressor from "compressorjs";
import { updateCategoriesCollection } from "../../Model/Category/CategoryUpdater";
import { getCategoriesTree } from "../CategoriesName/CategoriesData";
import { calculateFinalPrice } from "../../Utils/CurrencyUtils";
// No need for formatCategoriesForDropdown since we're handling the formatting directly

// Get categories from Firebase
export const getCategories = async () => {
  try {
    // Get the category tree to include subcategories
    const categoryTree = await getCategoriesTree();
    console.log("Category tree for PlaceAd:", categoryTree);

    // Extract only subcategory names
    const subcategoryNames = [];
    categoryTree.forEach((category) => {
      if (category.subcategories && category.subcategories.length > 0) {
        category.subcategories.forEach((subcat) => {
          if (subcat.name) {
            // Add subcategory with additional metadata
            // IMPORTANT: Make sure we're only using subcategories (items with parentId)
            if (subcat.parentId) {
              subcategoryNames.push({
                name: subcat.name.trim(), // Ensure name is trimmed
                parentName: category.name,
                parentId: category.id,
                title: subcat.title || null,
                isSubcategory: true,
              });
            }
          }
        });
      }
    });

    console.log("Subcategory names for PlaceAd:", subcategoryNames);

    // Sort alphabetically by name
    return subcategoryNames
      .sort((a, b) => a.name.localeCompare(b.name))
      .map((item) => item.name);
  } catch (error) {
    console.error("Error loading categories from Firebase:", error);

    // Fallback to static categories if Firebase fails
    try {
      // Import the categories data from the static file as fallback
      const categoriesData = require("../../Controller/CategoriesName/CategoriesName.js");
      const subcategories = [];

      // Process the first format (with sections) - only get items (subcategories)
      if (categoriesData.categories) {
        categoriesData.categories.forEach((category) => {
          if (category.sections) {
            category.sections.forEach((section) => {
              if (section.items) {
                section.items.forEach((item) => {
                  if (item.name) {
                    subcategories.push(item.name);
                  }
                });
              }
            });
          }
        });
      }

      // Process the second format (with supercategories) - only get items with supercategory
      if (categoriesData.additionalCategories) {
        categoriesData.additionalCategories.forEach((item) => {
          if (item.name && item.supercategory) {
            subcategories.push(item.name);
          }
        });
      }

      // Remove duplicates and sort
      return [...new Set(subcategories)].sort();
    } catch (fallbackError) {
      console.error("Error loading fallback categories:", fallbackError);
      return [];
    }
  }
};

// Get categories with their hierarchy information for advanced matching
export const getCategoriesWithHierarchy = async () => {
  try {
    // Get the category tree
    const categoryTree = await getCategoriesTree();

    // Create a mapping of subcategories to their parent categories
    const subcategoryToParent = {};

    categoryTree.forEach((category) => {
      if (category.subcategories && category.subcategories.length > 0) {
        category.subcategories.forEach((subcat) => {
          if (subcat.name) {
            subcategoryToParent[subcat.name.toLowerCase()] = {
              parentName: category.name,
              parentId: category.id,
              title: subcat.title || null,
            };
          }
        });
      }
    });

    return {
      categoryTree,
      subcategoryToParent,
    };
  } catch (error) {
    console.error("Error loading category hierarchy:", error);
    return {
      categoryTree: [],
      subcategoryToParent: {},
    };
  }
};

// Get user location from Firebase
export const getUserLocation = async (userId) => {
  try {
    if (!userId) {
      throw new Error("User ID is required");
    }

    const snapshot = await database.ref(`users/${userId}`).once("value");
    const userData = snapshot.val();

    if (userData && userData.location) {
      return userData.location;
    }

    return null;
  } catch (error) {
    console.error("Error fetching user location:", error);
    return null;
  }
};

// Compress image before upload
export const compressImage = (file) => {
  return new Promise((resolve, reject) => {
    new Compressor(file, {
      quality: 0.8,
      maxWidth: 1200,
      maxHeight: 1200,
      success(result) {
        // Create a new file with the original name
        const compressedFile = new File([result], file.name, {
          type: result.type,
          lastModified: Date.now(),
        });
        resolve(compressedFile);
      },
      error(err) {
        console.error("Compression error:", err);
        reject(err);
      },
    });
  });
};

// Upload photos to server
export const uploadAdPhotos = async (photos, email, brandName, adId) => {
  try {
    if (!photos || photos.length === 0) {
      return { success: false, message: "No photos to upload" };
    }

    if (!email) {
      return { success: false, message: "User email is required" };
    }

    if (!brandName) {
      return {
        success: false,
        message: "Brand name is required for folder organization",
      };
    }

    // Format brand name for folder structure
    const formattedBrandName = brandName.replace(/\s+/g, "-").toLowerCase();

    const uploadPromises = [];
    const uploadedUrls = [];

    // Process each photo
    for (const photo of photos) {
      try {
        // If photo already has a URL and is not marked for deletion, keep it
        if (photo.url && !photo.pendingDelete) {
          console.log("Keeping existing photo:", photo.url);
          uploadedUrls.push({
            url: photo.url,
            isCover: photo.isCover,
            id: photo.id || Math.random().toString(36).substring(2),
          });
          continue;
        }

        // If photo is marked for deletion, skip it (it will be deleted separately)
        if (photo.pendingDelete) {
          console.log(
            "Skipping photo marked for deletion:",
            photo.url || photo.id
          );
          continue;
        }

        // Skip photos without files
        if (!photo.file) {
          console.log("Skipping photo without file");
          continue;
        }

        // Compress the photo using compressor.js
        console.log("Compressing photo:", photo.file.name);
        const compressedPhoto = await compressImage(photo.file);
        console.log(
          "Compression complete. Original size:",
          photo.file.size,
          "Compressed size:",
          compressedPhoto.size
        );

        // Create form data
        const uploadFormData = new FormData();
        uploadFormData.append("photo", compressedPhoto);
        uploadFormData.append("email", email);
        uploadFormData.append("brandName", formattedBrandName); // Use formatted brand name
        uploadFormData.append("isCover", photo.isCover);
        uploadFormData.append("adId", adId); // Add adId for unique folder

        // Upload to server
        console.log("Uploading photo to server:", {
          email,
          brandName: formattedBrandName,
          isCover: photo.isCover,
          fileSize: compressedPhoto.size,
          fileName: compressedPhoto.name,
        });

        // Use the direct URL to the backend server
        const uploadPromise = axios.post(
          "http://localhost:5000/api/upload/ad-photo",
          uploadFormData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
            // Add withCredentials to handle CORS
            withCredentials: true,
          }
        );

        // Add error handling for the upload
        uploadPromise.catch((error) => {
          console.error(
            "Error uploading photo:",
            error.response ? error.response.data : error.message
          );
        });

        uploadPromises.push(uploadPromise);
      } catch (error) {
        console.error("Error processing photo:", error);
      }
    }

    // Wait for all uploads to complete
    const results = await Promise.all(
      uploadPromises.map((p) => p.catch((e) => e))
    );

    // Collect URLs from successful uploads
    results.forEach((response) => {
      // Skip errors
      if (response instanceof Error) {
        console.error("Upload failed:", response);
        return;
      }

      if (response.data && response.data.success) {
        uploadedUrls.push({
          url: response.data.data.url,
          public_id: response.data.data.public_id,
          isCover: response.data.data.isCover,
        });
      }
    });

    if (uploadedUrls.length === 0) {
      return { success: false, message: "Failed to upload photos" };
    }

    return { success: true, data: uploadedUrls };
  } catch (error) {
    console.error("Error uploading photos:", error);
    return { success: false, message: error.message };
  }
};

// Save ad to Firebase
export const saveAd = async (adData, status = "draft") => {
  try {
    const currentUser = auth.currentUser;

    if (!currentUser) {
      return { success: false, message: "User not authenticated" };
    }

    const userId = currentUser.uid;
    const email = currentUser.email;

    console.log("Starting to save ad with status:", status);
    console.log("Current user:", { userId, email });

    // Use existing adId or generate a new one
    // This ensures we use the same folder for updates
    const adId = adData.adId || database.ref().push().key;
    console.log("Using adId:", adId);

    // Process category information - we need to preserve parentId for subcategories
    if (adData.category) {
      console.log(
        "PLACE AD CONTROLLER - Original category value:",
        JSON.stringify(adData.category)
      );

      // Check if it's an object with proper subcategory structure
      if (typeof adData.category === "object") {
        // Check if it has the proper subcategory structure (name and parentId)
        if (adData.category.name && adData.category.parentId) {
          console.log(
            "PLACE AD CONTROLLER - Found proper subcategory with parentId:",
            adData.category.parentId
          );

          // Ensure name is trimmed
          if (typeof adData.category.name === "string") {
            adData.category.name = adData.category.name.trim();
          }

          // Keep the object structure with parentId for proper categorization
          console.log(
            "PLACE AD CONTROLLER - Using category object with parentId:",
            JSON.stringify(adData.category)
          );
        }
        // If it has a name but no parentId, it's a parent category
        else if (adData.category.name) {
          // Check if there's a supercategory property and log it
          if (adData.category.supercategory) {
            console.log(
              "PLACE AD CONTROLLER - WARNING: Found supercategory property:",
              adData.category.supercategory
            );
            console.log(
              "PLACE AD CONTROLLER - Removing supercategory property"
            );
            // Delete the supercategory property
            delete adData.category.supercategory;
          }

          // Extract just the name and trim it
          const categoryName = adData.category.name.trim();
          adData.category = categoryName;
          console.log(
            "PLACE AD CONTROLLER - Extracted category name from object:",
            adData.category
          );
        } else {
          // If it's an object without a name property, convert to string or use a default
          console.log(
            "PLACE AD CONTROLLER - Object without name property, using default"
          );
          adData.category = String(adData.category).trim() || "Uncategorized";
        }
      }
      // If it's a string, just trim it
      else if (typeof adData.category === "string") {
        const trimmedCategory = adData.category.trim();
        if (trimmedCategory !== adData.category) {
          console.log(
            "PLACE AD CONTROLLER - Trimming whitespace from category:",
            `'${adData.category}' -> '${trimmedCategory}'`
          );
          adData.category = trimmedCategory;
        }
      }

      console.log(
        "PLACE AD CONTROLLER - Final category value:",
        typeof adData.category === "object"
          ? JSON.stringify(adData.category)
          : adData.category
      );
    }

    // Handle photos
    let photoUrls = [];

    // If this is an update to an existing ad, get the previously saved photos
    let previousPhotos = [];
    if (adData.adId) {
      try {
        // Get the current ad data from Firebase
        const snapshot = await database
          .ref(`users/${userId}/ads/${adId}`)
          .once("value");
        const existingAdData = snapshot.val();

        if (existingAdData && existingAdData.photos) {
          previousPhotos = existingAdData.photos;
          console.log("Previously saved photos:", previousPhotos.length);
        }
      } catch (error) {
        console.error("Error getting previous photos:", error);
        // Continue even if we can't get previous photos
      }
    }

    // Check if this is an update to an existing ad and all photos were removed
    if (adData.adId && (!adData.photos || adData.photos.length === 0)) {
      console.log(
        "All photos removed from existing ad. Deleting from Cloudinary..."
      );

      try {
        // Delete all photos from Cloudinary
        const deleteResult = await deleteAdPhotos(
          email,
          adData.brandName,
          adId
        );
        console.log("All photos deletion result:", deleteResult);
      } catch (error) {
        console.error("Error deleting all photos:", error);
        // Continue with saving even if photo deletion fails
      }
    }
    // If there are explicitly marked photos for deletion, delete them
    else if (adData.photosToDelete && adData.photosToDelete.length > 0) {
      console.log(
        "Deleting explicitly marked photos:",
        adData.photosToDelete.length
      );

      // Delete each photo marked for deletion
      for (const photoUrl of adData.photosToDelete) {
        try {
          console.log("Deleting marked photo:", photoUrl);
          await deleteCloudinaryPhoto(email, adData.brandName, adId, photoUrl);
        } catch (error) {
          console.error("Error deleting marked photo:", error);
          // Continue with next photo even if one fails
        }
      }

      // Remove the photosToDelete array after processing
      delete adData.photosToDelete;
    }

    // Handle photos - separate existing photos from new ones
    if (adData.photos && adData.photos.length > 0) {
      console.log("Processing photos. Count:", adData.photos.length);

      // Separate existing photos from new ones
      const existingPhotos = adData.photos.filter(
        (photo) => photo.url && !photo.pendingDelete
      );
      const newPhotos = adData.photos.filter(
        (photo) => photo.file && !photo.url
      );

      console.log("Existing photos:", existingPhotos.length);
      console.log("New photos to upload:", newPhotos.length);

      // Add existing photos to photoUrls
      existingPhotos.forEach((photo) => {
        photoUrls.push({
          url: photo.url,
          isCover: photo.isCover,
          id: photo.id || Math.random().toString(36).substring(2),
        });
      });

      // Upload new photos if any
      if (newPhotos.length > 0) {
        const uploadResult = await uploadAdPhotos(
          newPhotos,
          email,
          adData.brandName,
          adId
        );

        if (uploadResult.success) {
          // Add newly uploaded photos to photoUrls
          photoUrls = [...photoUrls, ...uploadResult.data];
          console.log(
            "Photos uploaded successfully. Total photos:",
            photoUrls.length
          );
        } else {
          console.error("Failed to upload photos:", uploadResult.message);
          return { success: false, message: uploadResult.message };
        }
      }
    }

    // Fetch seller info from users collection
    const sellerSnapshot = await database.ref(`users/${userId}`).once("value");
    const sellerData = sellerSnapshot.val() || {};

    // Prepare ad data for saving
    const adToSave = {
      ...adData,
      adId: adId, // Store the adId
      photos: photoUrls,
      status: status, // "draft" or "published"
      createdAt: adData.createdAt || Date.now(),
      updatedAt: Date.now(),
      userId: userId,
      userEmail: email,
      currency: adData.currency || "EUR", // Ensure currency is saved
      isFreeItem: adData.isFreeItem || false, // Ensure isFreeItem is saved
      quantityInStock: adData.quantity || 1, // Set initial quantity in stock
      stockStatus: "In Stock", // Initial stock status
      sellerInfo: {
        displayName: sellerData.displayName || sellerData.username || "",
        email: sellerData.email || email || "",
        photoURL: sellerData.photoURL || sellerData.profilePicture || "",
        location: sellerData.location || "",
        phoneNumber: sellerData.phoneNumber || "",
        rating: sellerData.rating || 0,
        totalSales: sellerData.totalSales || 0,
        memberSince: sellerData.joinedSince || sellerData.createdAt || null
      }
    };

    // Delete the original photos array with File objects (can't be stored in Firebase)
    delete adToSave.photoFiles;

    // Remove file objects from photos array (can't be stored in Firebase)
    if (adToSave.photos && Array.isArray(adToSave.photos)) {
      adToSave.photos = adToSave.photos.map((photo) => {
        const { file, ...rest } = photo;
        return rest;
      });
    }

    console.log("Prepared ad data for saving:", {
      brandName: adToSave.brandName,
      status: adToSave.status,
      photoCount: adToSave.photos ? adToSave.photos.length : 0,
    });

    // Use the adId we determined earlier
    const newAdRef = adId
      ? database.ref(`users/${userId}/ads/${adId}`)
      : database.ref(`users/${userId}/ads`).push();
    console.log("Using ad ID:", adData.adId || newAdRef.key);

    // Save the ad
    await newAdRef.set(adToSave);
    console.log("Ad saved to Firebase successfully");

    // If the ad is published, update the categories collection
    if (status === "published") {
      console.log("Updating categories collection for published ad");
      await updateCategoriesCollection(adToSave);
    }

    // We no longer create order entries when publishing ads
    // Orders should only be created after payment is successful
    console.log(
      "Published ad saved to ads table only - no order entry created"
    );

    return {
      success: true,
      message: `Ad ${
        status === "published" ? "published" : "saved as draft"
      } successfully`,
      adId: newAdRef.key,
    };
  } catch (error) {
    console.error("Error saving ad:", error);
    return { success: false, message: error.message };
  }
};

// Get ad by ID
export const getAdById = async (adId) => {
  try {
    const currentUser = auth.currentUser;

    if (!currentUser) {
      return { success: false, message: "User not authenticated" };
    }

    const userId = currentUser.uid;
    const email = currentUser.email;

    // Get the ad from Firebase
    const snapshot = await database
      .ref(`users/${userId}/ads/${adId}`)
      .once("value");
    const adData = snapshot.val();

    if (!adData) {
      return { success: false, message: "Ad not found" };
    }

    console.log("Retrieved ad data from Firebase:", adData);

    // If there are no photos in Firebase or we need to ensure we have all photos from Cloudinary
    if (!adData.photos || adData.photos.length === 0) {
      console.log("No photos found in Firebase, checking Cloudinary...");

      // Create a folder path similar to what would be used in Cloudinary
      const brandName = adData.brandName || "";
      const folderPath = `user-photos/${email}/${brandName
        .replace(/\s+/g, "-")
        .toLowerCase()}-${adId}`;

      console.log("Looking for photos in Cloudinary folder:", folderPath);

      // If we have photos in Cloudinary but not in Firebase, we need to update Firebase
      // This would require a server endpoint, but for now we'll just use what's in Firebase
      // and log a warning
      console.warn(
        "Unable to check Cloudinary for photos. Using Firebase data only."
      );
    } else {
      console.log("Using photos from Firebase:", adData.photos);

      // Make sure all photos have the required properties
      if (adData.photos && adData.photos.length > 0) {
        adData.photos = adData.photos.map((photo) => ({
          url: photo.url || "",
          id: photo.id || Math.random().toString(36).substring(2),
          isCover: photo.isCover || false,
        }));

        // Make sure at least one photo is marked as cover
        if (!adData.photos.some((p) => p.isCover)) {
          adData.photos[0].isCover = true;
        }
      }
    }

    return { success: true, data: adData };
  } catch (error) {
    console.error("Error fetching ad:", error);
    return { success: false, message: error.message };
  }
};

// Load initial data (categories and user location)
export const loadInitialData = async () => {
  try {
    // Check if user is logged in
    const currentUser = auth.currentUser;
    if (!currentUser) {
      return {
        success: false,
        message: "Please log in to place an ad",
        redirect: "/login",
      };
    }

    // Load categories
    const categoriesList = await getCategories();

    // Load category hierarchy for advanced matching
    const categoryHierarchy = await getCategoriesWithHierarchy();

    // Load user location
    const location = await getUserLocation(currentUser.uid);
    let shippingFrom = "";

    if (location && location.formattedLocation) {
      shippingFrom = location.formattedLocation;
    }

    return {
      success: true,
      data: {
        categories: categoriesList,
        categoryHierarchy,
        location,
        shippingFrom,
      },
    };
  } catch (error) {
    console.error("Error loading initial data:", error);
    return { success: false, message: error.message };
  }
};

// Validate form data
export const validateForm = (formData) => {
  console.log("validateForm called with formData:", formData);
  const errors = {};

  // Required fields - check if they exist and are not empty
  if (!formData.category) errors.category = "Category is required";
  if (!formData.brandName) errors.brandName = "Brand name is required";
  if (!formData.description) errors.description = "Description is required";
  if (!formData.shippingFrom)
    errors.shippingFrom = "Shipping location is required";
  if (!formData.condition) errors.condition = "Condition is required";
  if (!formData.sellerType) errors.sellerType = "Seller type is required";

  // Price validation - check if it exists, is not empty, and is a valid number
  // Skip price validation if it's marked as a free item
  if (!formData.isFreeItem) {
    if (!formData.price) {
      errors.price = "Price is required";
    } else if (isNaN(Number(formData.price)) || Number(formData.price) < 0) {
      errors.price = "Price must be a positive number";
    }
  }

  // Quantity validation - check if it exists, is not empty, and is a valid number
  if (!formData.quantity) {
    errors.quantity = "Quantity is required";
  } else if (
    isNaN(Number(formData.quantity)) ||
    Number(formData.quantity) <= 0
  ) {
    errors.quantity = "Quantity must be a positive number";
  }

  // Discount validation - only check if it's a number between 0 and 100
  if (
    formData.discount !== undefined &&
    (isNaN(Number(formData.discount)) ||
      Number(formData.discount) < 0 ||
      Number(formData.discount) > 100)
  ) {
    errors.discount = "Discount must be between 0 and 100";
  }

  const isValid = Object.keys(errors).length === 0;
  console.log("Validation result:", isValid, "Errors:", errors);

  return {
    isValid,
    errors,
  };
};

// Handle photo upload
export const processPhotoUpload = (files, currentPhotos) => {
  if (currentPhotos.length + files.length > 6) {
    return {
      success: false,
      message: "Maximum 6 photos allowed",
    };
  }

  const newPhotos = files.map((file) => ({
    file,
    preview: URL.createObjectURL(file),
    isCover: currentPhotos.length === 0, // First photo is cover by default
  }));

  return {
    success: true,
    photos: [...currentPhotos, ...newPhotos],
  };
};

// Set photo as cover
export const updateCoverPhoto = (photos, index) => {
  return photos.map((photo, i) => ({
    ...photo,
    isCover: i === index,
  }));
};

// Delete photo
export const deletePhoto = async (photos, index, isExisting = false) => {
  const newPhotos = [...photos];
  const photo = newPhotos[index];

  // Check if deleting the cover photo
  const wasCover = photo.isCover;

  // If it's an existing photo from Cloudinary, delete it from the server
  if (isExisting && photo.url) {
    try {
      const currentUser = auth.currentUser;
      if (currentUser && photo.adId) {
        // Store the photo details for deletion
        photo.pendingDelete = true;
      }
    } catch (error) {
      console.error("Error marking photo for deletion:", error);
    }
  }

  // Release object URL to prevent memory leaks if it's a local preview
  if (photo.preview && !photo.url) {
    URL.revokeObjectURL(photo.preview);
  }

  // Remove the photo
  newPhotos.splice(index, 1);

  // If we deleted the cover and have remaining photos, set the first one as cover
  if (wasCover && newPhotos.length > 0) {
    newPhotos[0].isCover = true;
  }

  return newPhotos;
};

// Delete all photos for an ad from Cloudinary
export const deleteAdPhotos = async (email, brandName, adId) => {
  try {
    console.log("Deleting all photos from Cloudinary:", {
      email,
      brandName,
      adId,
    });

    // Make API call to delete all photos
    const response = await fetch(
      "http://localhost:5000/api/ads/delete-photos",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          brandName,
          adId,
        }),
        credentials: "include",
      }
    );

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("Error deleting all photos:", error);
    return { success: false, message: error.message };
  }
};

// Delete a single photo from Cloudinary
export const deleteCloudinaryPhoto = async (
  email,
  brandName,
  adId,
  photoUrl
) => {
  try {
    console.log("Deleting photo from Cloudinary:", {
      email,
      brandName,
      adId,
      photoUrl,
    });

    // Extract the public_id from the URL
    const urlParts = photoUrl.split("/");
    const filenameWithExt = urlParts[urlParts.length - 1];
    const filename = filenameWithExt.split(".")[0];

    console.log("Extracted filename:", filename);

    // Make API call to delete the photo
    const response = await fetch("http://localhost:5000/api/ads/delete-photo", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email,
        brandName,
        adId,
        filename,
      }),
      credentials: "include",
    });

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("Error deleting photo from Cloudinary:", error);
    return { success: false, message: error.message };
  }
};

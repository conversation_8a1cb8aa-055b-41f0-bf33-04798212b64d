import {
  TextField,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  useMediaQuery,
  useTheme,
  FormHelperText,
} from "@mui/material";
import LocalShippingIcon from "@mui/icons-material/LocalShipping";

const ShippingSection = ({
  formData,
  formErrors,
  onInputChange,
  onShippingMethodChange,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const sharedInputStyle = {
    "& .MuiOutlinedInput-root": {
      borderRadius: "6px",
    },
    "& .MuiOutlinedInput-notchedOutline": {
      borderWidth: "1px",
    },
  };

  const sharedSelectStyle = {
    "& .MuiOutlinedInput-root": {
      borderRadius: "6px",
      borderWidth: "1px",
    },
    "& .MuiOutlinedInput-notchedOutline": {
      borderWidth: "1px",
    },
  };

  return (
    <Box>
      <Typography
        variant="h6"
        gutterBottom
        sx={{ display: "flex", alignItems: "center", mb: 2 }}
      >
        <LocalShippingIcon sx={{ mr: 1 }} />
        Shipping Details
      </Typography>

      {/* Shipping From */}
      <Box mb={isMobile ? 3 : 4}>
        <TextField
          name="shippingFrom"
          label="Shipping From *"
          variant="outlined"
          fullWidth
          required
          value={formData.shippingFrom}
          onChange={onInputChange}
          error={!!formErrors.shippingFrom}
          helperText={formErrors.shippingFrom}
          placeholder="Enter your location"
          sx={sharedInputStyle}
        />
      </Box>

      {/* Shipping Method */}
      <Box mb={isMobile ? 3 : 4}>
        <FormControl fullWidth required sx={sharedSelectStyle}>
          <InputLabel>Shipping Method *</InputLabel>
          <Select
            name="shippingMethod"
            value={formData.shippingMethod}
            onChange={onShippingMethodChange}
            label="Shipping Method *"
            MenuProps={{
              PaperProps: {
                style: { maxHeight: 300 },
              },
            }}
          >
            <MenuItem value="Local delivery">Local Delivery</MenuItem>
            <MenuItem value="collection">Collection</MenuItem>
            <MenuItem value="post courier">Post/Courier</MenuItem>
            <MenuItem value="to_be_arranged">To Be Arranged</MenuItem>
            <MenuItem value="Free Shipping">Free Shipping</MenuItem>
            <MenuItem value="Standard Shipping">Standard Shipping</MenuItem>
            <MenuItem value="Express Shipping">Express Shipping</MenuItem>
            <MenuItem value="International Post">International Post</MenuItem>
            <MenuItem value="EMS">EMS (Express Mail Service)</MenuItem>
            <MenuItem value="FedEx">FedEx</MenuItem>
            <MenuItem value="UPS">UPS</MenuItem>
            <MenuItem
              value="Other
            international shipping 2-3 days dispatch"
            >
              Other international shipping 2-3 days dispatch
            </MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Return Option */}
      <Box mb={isMobile ? 3 : 4}>
        <FormControl fullWidth required sx={sharedSelectStyle}>
          <InputLabel>Return *</InputLabel>
          <Select
            name="returnOption"
            value={formData.returnOption}
            onChange={onInputChange}
            label="Return *"
            MenuProps={{
              PaperProps: {
                style: { maxHeight: 300 },
              },
            }}
          >
            <MenuItem value="seller_pays_return">Seller Pays Return</MenuItem>
            <MenuItem value="buyer_pays_return">Buyer Pays Return</MenuItem>
            <MenuItem value="free_return">Free Return</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Payment for Return (conditional) */}
      {(formData.shippingMethod === "local delivery" ||
        formData.shippingMethod === "post courier") && (
        <Box mb={isMobile ? 3 : 4}>
          <FormControl fullWidth required sx={sharedSelectStyle}>
            <InputLabel>Payment for return *</InputLabel>
            <Select
              name="shippingPayer"
              value={formData.shippingPayer}
              onChange={onInputChange}
              label="Payment for return *"
              MenuProps={{
                PaperProps: {
                  style: { maxHeight: 300 },
                },
              }}
            >
              <MenuItem value="buyer">Buyer Pays Postage</MenuItem>
              <MenuItem value="seller">Free Shipping</MenuItem>
            </Select>
          </FormControl>
        </Box>
      )}

      {/* Estimated Delivery Time */}
      <Box mb={isMobile ? 3 : 4}>
        <FormControl fullWidth sx={sharedSelectStyle}>
          <InputLabel>Estimated Delivery Time</InputLabel>
          <Select
            name="estimatedDelivery"
            value={formData.estimatedDelivery || ""}
            onChange={onInputChange}
            label="Estimated Delivery Time"
            MenuProps={{
              PaperProps: {
                style: { maxHeight: 300 },
              },
            }}
          >
            <MenuItem value="1-2 days">1-2 Business Days</MenuItem>
            <MenuItem value="3-5 days">3-5 Business Days</MenuItem>
            <MenuItem value="1-2 weeks">1-2 Weeks</MenuItem>
            <MenuItem value="2-4 weeks">2-4 Weeks</MenuItem>
            <MenuItem value="over 4 weeks">Over 4 Weeks</MenuItem>
          </Select>
          <FormHelperText>
            Let buyers know when to expect delivery
          </FormHelperText>
        </FormControl>
      </Box>
    </Box>
  );
};

export default ShippingSection;

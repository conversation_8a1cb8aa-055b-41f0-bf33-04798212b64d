import { useState, useEffect } from "react";
import {
  <PERSON>,
  But<PERSON>,
  Chip,
  Menu,
  MenuItem,
  Typography,
  useMediaQuery,
  useTheme,
  Paper,
  TextField,
  Autocomplete,
} from "@mui/material";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import SortIcon from "@mui/icons-material/Sort";
import LocalOfferIcon from "@mui/icons-material/LocalOffer";
import LocationOnIcon from "@mui/icons-material/LocationOn";

// Import country-state data
import countryStateData from "../../../Controller/CountryStates/Country-State-Data-In-JSON.json";

/**
 * CategoryFilterBar component that displays filter options: Sort by, Discount Items, Item Location
 * @param {Object} props - Component props
 * @param {Function} props.onCategorySelect - Callback function when a filter is selected
 */
const CategoryFilterBar = ({ onCategorySelect }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  // State for filters
  const [selectedSort, setSelectedSort] = useState(null);
  const [showDiscountItems, setShowDiscountItems] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [locationInput, setLocationInput] = useState("");

  // Restore filter states from localStorage on component mount
  useEffect(() => {
    const restoreFilterStates = () => {
      console.log(
        "🔄 CategoryFilterBar: Restoring filter states from localStorage"
      );

      try {
        // Restore sort filter
        const storedSort = localStorage.getItem("filterSort");
        if (storedSort) {
          const parsedSort = JSON.parse(storedSort);
          setSelectedSort(parsedSort);
          console.log(
            "🔄 CategoryFilterBar: Restored sort filter:",
            parsedSort
          );
        } else {
          console.log("🔄 CategoryFilterBar: No stored sort filter found");
        }

        // Restore discount filter
        const storedDiscount = localStorage.getItem("filterDiscount");
        if (storedDiscount) {
          const parsedDiscount = JSON.parse(storedDiscount);
          setShowDiscountItems(parsedDiscount);
          console.log(
            "🔄 CategoryFilterBar: Restored discount filter:",
            parsedDiscount
          );
        } else {
          console.log("🔄 CategoryFilterBar: No stored discount filter found");
        }

        // Restore location filter
        const storedLocation = localStorage.getItem("filterLocation");
        if (storedLocation) {
          const parsedLocation = JSON.parse(storedLocation);
          setSelectedLocation(parsedLocation);
          setLocationInput(parsedLocation.name || "");
          console.log(
            "🔄 CategoryFilterBar: Restored location filter:",
            parsedLocation
          );
        } else {
          console.log("🔄 CategoryFilterBar: No stored location filter found");
        }
      } catch (error) {
        console.error(
          "🔄 CategoryFilterBar: Error restoring filter states:",
          error
        );
      }
    };

    restoreFilterStates();
  }, []);

  // Apply restored filters when ads are available
  useEffect(() => {
    const allAdsFromStorage = localStorage.getItem("allAds");

    if (
      allAdsFromStorage &&
      (selectedSort || showDiscountItems || selectedLocation)
    ) {
      console.log(
        "CategoryFilterBar: Applying restored filters to available ads"
      );

      // Apply the current filter states
      applyFilters({
        sort: selectedSort,
        discount: showDiscountItems,
        location: selectedLocation,
      });
    }
  }, [selectedSort, showDiscountItems, selectedLocation]);

  // State for menu anchors
  const [sortAnchorEl, setSortAnchorEl] = useState(null);

  // Apply filters to a given set of ads
  const applyFiltersToAds = (ads) => {
    // Store the ads in localStorage first
    localStorage.setItem("allAds", JSON.stringify(ads));

    let filteredAds = [...ads];

    // Apply discount filter
    if (showDiscountItems) {
      filteredAds = filteredAds.filter((ad) => {
        const discount = parseFloat(ad.discount || 0);
        return discount > 0;
      });
    }

    // Apply location filter
    if (selectedLocation) {
      filteredAds = filteredAds.filter((ad) => {
        const sellerLocation = ad.sellerInfo?.location?.formattedLocation || "";
        const shippingFrom = ad.shippingFrom || "";
        const sellerAddress = ad.sellerInfo?.location?.address || "";
        const sellerCountry = ad.sellerInfo?.location?.country?.name || "";
        const sellerCity = ad.sellerInfo?.location?.state?.name || "";
        const shippingMethod = ad.shippingMethod || "";
        const estimatedDelivery = ad.estimatedDelivery || "";

        // Combine all location fields for searching
        const allLocationText =
          `${sellerLocation} ${shippingFrom} ${sellerAddress} ${sellerCountry} ${sellerCity} ${shippingMethod} ${estimatedDelivery}`.toLowerCase();

        switch (selectedLocation.type) {
          case "predefined":
            switch (selectedLocation.id) {
              case "local":
                return (
                  allLocationText.includes("local") ||
                  allLocationText.includes("same")
                );
              case "national":
                return !allLocationText.includes("international");
              case "international":
                return (
                  allLocationText.includes("international") ||
                  allLocationText.includes("canada") ||
                  allLocationText.includes("france") ||
                  allLocationText.includes("uk") ||
                  allLocationText.includes("germany") ||
                  allLocationText.includes("australia")
                );
              default:
                return true;
            }
          case "country":
            // Filter by country name
            return allLocationText.includes(
              selectedLocation.name.toLowerCase()
            );
          case "city":
            // Filter by city name or country name
            return (
              allLocationText.includes(
                selectedLocation.city_name.toLowerCase()
              ) ||
              allLocationText.includes(
                selectedLocation.country_name.toLowerCase()
              )
            );
          default:
            return true;
        }
      });
    }

    // Apply sorting
    if (selectedSort) {
      console.log("Applying sort in applyFiltersToAds:", selectedSort.name);

      filteredAds.sort((a, b) => {
        const priceA = calculateEffectivePrice(a);
        const priceB = calculateEffectivePrice(b);

        // Get timestamps - prioritize updatedAt, then createdAt
        const dateA = parseFloat(a.updatedAt || a.createdAt || 0);
        const dateB = parseFloat(b.updatedAt || b.createdAt || 0);

        switch (selectedSort.id) {
          case "recent":
            // Most recent first (higher timestamp = more recent)
            return dateB - dateA;
          case "price_low":
            // Low to high price
            return priceA - priceB;
          case "price_high":
            // High to low price
            return priceB - priceA;
          default:
            // Default to most recent
            return dateB - dateA;
        }
      });
    }

    // Store filtered results as search results
    localStorage.setItem("searchResults", JSON.stringify(filteredAds));
    localStorage.setItem("searchTimestamp", Date.now().toString());

    // Dispatch event to update the Home component
    document.dispatchEvent(new CustomEvent("storageUpdated"));
  };

  // Sort options
  const sortOptions = [
    { id: "recent", name: "Most recent" },
    { id: "price_low", name: "Price: low to high" },
    { id: "price_high", name: "Price: high to low" },
  ];

  // Create location options from country-state data
  const createLocationOptions = () => {
    const options = [];

    // Add predefined options
    options.push(
      { id: "local", name: "Local items", type: "predefined" },
      { id: "national", name: "National", type: "predefined" },
      { id: "international", name: "International", type: "predefined" }
    );

    // Add countries and cities from JSON data
    countryStateData.forEach((country) => {
      // Add country
      options.push({
        id: `country_${country.country_id}`,
        name: country.country_name,
        type: "country",
        country_id: country.country_id,
        searchText: country.country_name.toLowerCase(),
      });

      // Add cities
      if (country.states && country.states.length > 0) {
        country.states.forEach((city) => {
          options.push({
            id: `city_${city.state_id}`,
            name: `${city.state_name}, ${country.country_name}`,
            type: "city",
            city_id: city.state_id,
            country_id: country.country_id,
            country_name: country.country_name,
            city_name: city.state_name,
            searchText: `${city.state_name.toLowerCase()} ${country.country_name.toLowerCase()}`,
          });
        });
      }
    });

    return options;
  };

  const locationOptions = createLocationOptions();

  // Listen for events from Home component
  useEffect(() => {
    const handleAllAdsReceived = (event) => {
      const allAds = event.detail;
      if (allAds && allAds.length > 0) {
        // Apply current filters to all ads
        applyFiltersToAds(allAds);
      }
    };

    document.addEventListener("allAdsReceived", handleAllAdsReceived);

    return () => {
      document.removeEventListener("allAdsReceived", handleAllAdsReceived);
    };
  }, []); // Remove dependencies to prevent infinite loop

  // Apply filters to current ads (search results or all ads)
  const applyFilters = (newFilters = {}) => {
    console.log("🔧 CategoryFilterBar: applyFilters called with:", newFilters);

    // Get current ads from localStorage (search results or all ads)
    const searchTerm = localStorage.getItem("searchTerm");
    const originalSearchResults = localStorage.getItem("originalSearchResults");
    const allAdsFromStorage = localStorage.getItem("allAds");

    console.log("🔧 CategoryFilterBar: Storage state:", {
      hasSearchTerm: !!searchTerm,
      searchTerm,
      hasOriginalSearchResults: !!originalSearchResults,
      hasAllAds: !!allAdsFromStorage,
    });

    let adsToFilter = [];

    if (searchTerm && originalSearchResults) {
      // If there's a search term, use original search results (before any filters)
      try {
        adsToFilter = JSON.parse(originalSearchResults);
        console.log(
          "🔧 CategoryFilterBar: Filtering original search results:",
          adsToFilter.length,
          "ads"
        );
      } catch (error) {
        console.error(
          "🔧 CategoryFilterBar: Error parsing original search results:",
          error
        );
        return;
      }
    } else if (allAdsFromStorage) {
      // If no search but we have all ads in storage, filter those
      try {
        adsToFilter = JSON.parse(allAdsFromStorage);
        console.log(
          "🔧 CategoryFilterBar: Filtering all ads:",
          adsToFilter.length,
          "ads"
        );
      } catch (error) {
        console.error("🔧 CategoryFilterBar: Error parsing all ads:", error);
        return;
      }
    } else {
      // If no ads available, request all ads from Home component
      console.log(
        "🔧 CategoryFilterBar: No ads available, requesting all ads for filtering"
      );
      document.dispatchEvent(new CustomEvent("requestAllAds"));
      return;
    }

    // Apply current filters plus any new filters
    const currentSort =
      newFilters.sort !== undefined ? newFilters.sort : selectedSort;
    const currentDiscount =
      newFilters.discount !== undefined
        ? newFilters.discount
        : showDiscountItems;
    const currentLocation =
      newFilters.location !== undefined
        ? newFilters.location
        : selectedLocation;

    let filteredAds = [...adsToFilter];

    // Apply discount filter
    if (currentDiscount) {
      filteredAds = filteredAds.filter((ad) => {
        const discount = parseFloat(ad.discount || 0);
        return discount > 0;
      });
    }

    // Apply location filter
    if (currentLocation) {
      console.log("Applying location filter:", currentLocation.name);

      filteredAds = filteredAds.filter((ad) => {
        const sellerLocation = ad.sellerInfo?.location?.formattedLocation || "";
        const shippingFrom = ad.shippingFrom || "";
        const sellerAddress = ad.sellerInfo?.location?.address || "";
        const sellerCountry = ad.sellerInfo?.location?.country?.name || "";
        const sellerCity = ad.sellerInfo?.location?.state?.name || "";
        const shippingMethod = ad.shippingMethod || "";
        const estimatedDelivery = ad.estimatedDelivery || "";

        // Combine all location fields for searching
        const allLocationText =
          `${sellerLocation} ${shippingFrom} ${sellerAddress} ${sellerCountry} ${sellerCity} ${shippingMethod} ${estimatedDelivery}`.toLowerCase();

        switch (currentLocation.type) {
          case "predefined":
            switch (currentLocation.id) {
              case "local":
                return (
                  allLocationText.includes("local") ||
                  allLocationText.includes("same")
                );
              case "national":
                return !allLocationText.includes("international");
              case "international":
                return (
                  allLocationText.includes("international") ||
                  allLocationText.includes("canada") ||
                  allLocationText.includes("france") ||
                  allLocationText.includes("uk") ||
                  allLocationText.includes("germany") ||
                  allLocationText.includes("australia")
                );
              default:
                return true;
            }
          case "country":
            // Filter by country name
            return allLocationText.includes(currentLocation.name.toLowerCase());
          case "city":
            // Filter by city name or country name
            return (
              allLocationText.includes(
                currentLocation.city_name.toLowerCase()
              ) ||
              allLocationText.includes(
                currentLocation.country_name.toLowerCase()
              )
            );
          default:
            return true;
        }
      });
    }

    // Apply sorting
    if (currentSort) {
      console.log("Applying sort:", currentSort.name);

      filteredAds.sort((a, b) => {
        const priceA = calculateEffectivePrice(a);
        const priceB = calculateEffectivePrice(b);

        // Get timestamps - prioritize updatedAt, then createdAt
        const dateA = parseFloat(a.updatedAt || a.createdAt || 0);
        const dateB = parseFloat(b.updatedAt || b.createdAt || 0);

        switch (currentSort.id) {
          case "recent":
            // Most recent first (higher timestamp = more recent)
            return dateB - dateA;
          case "price_low":
            // Low to high price
            return priceA - priceB;
          case "price_high":
            // High to low price
            return priceB - priceA;
          default:
            // Default to most recent
            return dateB - dateA;
        }
      });

      console.log(`Sorted ${filteredAds.length} ads by ${currentSort.name}`);
    }

    console.log(
      "🔧 CategoryFilterBar: Final filtered results:",
      filteredAds.length,
      "ads"
    );

    // Store filtered results
    localStorage.setItem("searchResults", JSON.stringify(filteredAds));
    localStorage.setItem("searchTimestamp", Date.now().toString());

    console.log(
      "🔧 CategoryFilterBar: Stored filtered results in localStorage"
    );
    console.log("🔧 CategoryFilterBar: Dispatching storageUpdated event");

    // Dispatch event to update the Home component
    document.dispatchEvent(new CustomEvent("storageUpdated"));
  };

  // Calculate effective price (considering discounts)
  const calculateEffectivePrice = (ad) => {
    const originalPrice = parseFloat(ad.price || 0);
    const discount = parseFloat(ad.discount || 0);

    if (discount > 0) {
      return originalPrice * (1 - discount / 100);
    }

    return originalPrice;
  };

  // Handle sort menu
  const handleSortMenuOpen = (event) => {
    setSortAnchorEl(event.currentTarget);
  };

  const handleSortMenuClose = () => {
    setSortAnchorEl(null);
  };

  const handleSortSelect = (sortOption) => {
    setSelectedSort(sortOption);
    handleSortMenuClose();

    // Persist sort filter
    localStorage.setItem("filterSort", JSON.stringify(sortOption));

    // Apply filters with new sort option
    applyFilters({ sort: sortOption });

    // Call the callback with sort information
    if (onCategorySelect) {
      onCategorySelect({ type: "sort", value: sortOption.id });
    }
  };

  // Handle discount toggle
  const handleDiscountToggle = (event) => {
    const isChecked = event.target.checked;
    setShowDiscountItems(isChecked);

    // Persist discount filter
    localStorage.setItem("filterDiscount", JSON.stringify(isChecked));

    // Apply filters with new discount setting
    applyFilters({ discount: isChecked });

    // Call the callback with discount information
    if (onCategorySelect) {
      onCategorySelect({ type: "discount", value: isChecked });
    }
  };

  // Check if menus are open
  const isSortMenuOpen = Boolean(sortAnchorEl);

  return (
    <Paper
      elevation={0}
      sx={{
        width: "100%",
        borderRadius: 0,
        borderBottom: "1px solid",
        borderColor: "divider",
        backgroundColor: "#f8f8f8",
        overflow: "auto",
        position: "sticky",
        top: 0,
        zIndex: 900,
      }}
    >
      <Box
        sx={{
          display: "flex",
          overflowX: "auto",
          py: 0.5,
          px: 2,
          "&::-webkit-scrollbar": {
            height: "4px",
          },
          "&::-webkit-scrollbar-track": {
            backgroundColor: "transparent",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "#bdbdbd",
            borderRadius: "4px",
          },
        }}
      >
        {/* Sort By Filter */}
        <Button
          color="inherit"
          endIcon={<KeyboardArrowDownIcon />}
          onClick={handleSortMenuOpen}
          aria-controls={isSortMenuOpen ? "sort-menu" : undefined}
          aria-haspopup="true"
          aria-expanded={isSortMenuOpen ? "true" : undefined}
          startIcon={<SortIcon />}
          sx={{
            textTransform: "none",
            minWidth: "auto",
            px: 1.5,
            py: 0.5,
            fontSize: isMobile ? "0.75rem" : "0.875rem",
            whiteSpace: "nowrap",
            color: selectedSort ? "primary.main" : "text.primary",
            fontWeight: selectedSort ? 600 : 400,
            mr: 1,
          }}
        >
          Sort by
        </Button>

        <Menu
          id="sort-menu"
          anchorEl={sortAnchorEl}
          open={isSortMenuOpen}
          onClose={handleSortMenuClose}
          slotProps={{
            paper: {
              elevation: 3,
              sx: {
                maxHeight: 300,
                width: isMobile ? 200 : 250,
              },
            },
          }}
        >
          {sortOptions.map((option) => (
            <MenuItem
              key={option.id}
              onClick={() => handleSortSelect(option)}
              sx={{
                fontSize: isMobile ? "0.75rem" : "0.875rem",
                py: 0.75,
                fontWeight:
                  selectedSort && selectedSort.id === option.id ? 600 : 400,
                color:
                  selectedSort && selectedSort.id === option.id
                    ? "primary.main"
                    : "text.primary",
              }}
            >
              {option.name}
            </MenuItem>
          ))}
        </Menu>

        {/* Discount Items Filter */}
        <Button
          color="inherit"
          onClick={() =>
            handleDiscountToggle({ target: { checked: !showDiscountItems } })
          }
          startIcon={
            <LocalOfferIcon color={showDiscountItems ? "primary" : "inherit"} />
          }
          sx={{
            textTransform: "none",
            minWidth: "auto",
            px: 1.5,
            py: 0.5,
            fontSize: isMobile ? "0.75rem" : "0.875rem",
            whiteSpace: "nowrap",
            color: showDiscountItems ? "primary.main" : "text.primary",
            fontWeight: showDiscountItems ? 600 : 400,
            mr: 1,
          }}
        >
          Discount Items
        </Button>

        {/* Location Filter with Autocomplete */}
        <Box sx={{ minWidth: isMobile ? 200 : 250, mr: 1 }}>
          <Autocomplete
            size="small"
            options={locationOptions}
            getOptionLabel={(option) => option.name}
            value={selectedLocation}
            onChange={(_, newValue) => {
              setSelectedLocation(newValue);
              if (newValue) {
                // Persist location filter
                localStorage.setItem(
                  "filterLocation",
                  JSON.stringify(newValue)
                );

                applyFilters({ location: newValue });
                if (onCategorySelect) {
                  onCategorySelect({ type: "location", value: newValue.id });
                }
              } else {
                // Clear location filter
                localStorage.removeItem("filterLocation");

                applyFilters({ location: null });
                if (onCategorySelect) {
                  onCategorySelect({ type: "location", value: null });
                }
              }
            }}
            inputValue={locationInput}
            onInputChange={(_, newInputValue) => {
              setLocationInput(newInputValue);
            }}
            filterOptions={(options, { inputValue }) => {
              if (!inputValue) return options.slice(0, 50); // Show first 50 options when no input

              const filtered = options.filter(
                (option) =>
                  option.searchText?.includes(inputValue.toLowerCase()) ||
                  option.name.toLowerCase().includes(inputValue.toLowerCase())
              );

              return filtered.slice(0, 50); // Limit to 50 results for performance
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                placeholder="Search location..."
                variant="outlined"
                slotProps={{
                  input: {
                    ...params.InputProps,
                    startAdornment: (
                      <LocationOnIcon sx={{ mr: 1, color: "text.secondary" }} />
                    ),
                    sx: {
                      fontSize: isMobile ? "0.75rem" : "0.875rem",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: selectedLocation
                          ? "primary.main"
                          : "divider",
                      },
                    },
                  },
                }}
                sx={{
                  "& .MuiInputBase-root": {
                    height: 36,
                    fontSize: isMobile ? "0.75rem" : "0.875rem",
                  },
                  "& .MuiInputLabel-root": {
                    fontSize: isMobile ? "0.75rem" : "0.875rem",
                  },
                }}
              />
            )}
            renderOption={(props, option) => (
              <Box component="li" {...props} key={option.id}>
                <Box>
                  <Typography
                    variant="body2"
                    sx={{
                      fontSize: isMobile ? "0.75rem" : "0.875rem",
                      fontWeight: option.type === "predefined" ? 600 : 400,
                      color:
                        option.type === "predefined"
                          ? "primary.main"
                          : "text.primary",
                    }}
                  >
                    {option.name}
                  </Typography>
                  {option.type !== "predefined" && (
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: "0.7rem",
                        color: "text.secondary",
                        textTransform: "capitalize",
                      }}
                    >
                      {option.type}
                    </Typography>
                  )}
                </Box>
              </Box>
            )}
            noOptionsText="No locations found"
            clearOnBlur={false}
            selectOnFocus
            handleHomeEndKeys
            sx={{
              "& .MuiAutocomplete-inputRoot": {
                color: selectedLocation ? "primary.main" : "text.primary",
                fontWeight: selectedLocation ? 600 : 400,
              },
            }}
          />
        </Box>
      </Box>

      {(selectedSort || showDiscountItems || selectedLocation) && (
        <Box
          sx={{
            px: 2,
            py: 0.5,
            display: "flex",
            alignItems: "center",
            backgroundColor: "#fff",
            flexWrap: "wrap",
          }}
        >
          <Typography
            variant="body2"
            sx={{ mr: 1, color: "text.secondary", fontSize: "0.75rem" }}
          >
            Active filters:
          </Typography>

          {/* Sort filter chip */}
          {selectedSort && (
            <Chip
              label={`Sort: ${selectedSort.name}`}
              size="small"
              onDelete={() => {
                setSelectedSort(null);
                localStorage.removeItem("filterSort");
                applyFilters({ sort: null });
                if (onCategorySelect)
                  onCategorySelect({ type: "sort", value: null });
              }}
              sx={{
                height: 24,
                mr: 0.5,
                mb: 0.5,
                "& .MuiChip-label": {
                  fontSize: "0.75rem",
                  px: 1,
                },
              }}
            />
          )}

          {/* Discount filter chip */}
          {showDiscountItems && (
            <Chip
              label="Discount items only"
              size="small"
              onDelete={() => {
                setShowDiscountItems(false);
                localStorage.removeItem("filterDiscount");
                applyFilters({ discount: false });
                if (onCategorySelect)
                  onCategorySelect({ type: "discount", value: false });
              }}
              sx={{
                height: 24,
                mr: 0.5,
                mb: 0.5,
                "& .MuiChip-label": {
                  fontSize: "0.75rem",
                  px: 1,
                },
              }}
            />
          )}

          {/* Location filter chip */}
          {selectedLocation && (
            <Chip
              label={`Location: ${selectedLocation.name}`}
              size="small"
              onDelete={() => {
                setSelectedLocation(null);
                setLocationInput("");
                localStorage.removeItem("filterLocation");
                applyFilters({ location: null });
                if (onCategorySelect)
                  onCategorySelect({ type: "location", value: null });
              }}
              sx={{
                height: 24,
                mr: 0.5,
                mb: 0.5,
                "& .MuiChip-label": {
                  fontSize: "0.75rem",
                  px: 1,
                },
              }}
            />
          )}

          {/* Clear all filters button */}
          {(selectedSort || showDiscountItems || selectedLocation) && (
            <Button
              variant="text"
              size="small"
              onClick={() => {
                setSelectedSort(null);
                setShowDiscountItems(false);
                setSelectedLocation(null);
                setLocationInput("");

                // Clear persisted filter states
                localStorage.removeItem("filterSort");
                localStorage.removeItem("filterDiscount");
                localStorage.removeItem("filterLocation");

                // Clear all filters and restore original results
                const searchTerm = localStorage.getItem("searchTerm");
                if (searchTerm) {
                  // If there's a search term, re-run the search without filters
                  document.dispatchEvent(new CustomEvent("rerunSearch"));
                } else {
                  // If no search term, show all ads
                  localStorage.removeItem("searchResults");
                  localStorage.setItem(
                    "searchTimestamp",
                    Date.now().toString()
                  );
                  document.dispatchEvent(new CustomEvent("storageUpdated"));
                }

                if (onCategorySelect)
                  onCategorySelect({ type: "clear", value: null });
              }}
              sx={{
                ml: "auto",
                fontSize: "0.75rem",
                textTransform: "none",
              }}
            >
              Clear all
            </Button>
          )}
        </Box>
      )}
    </Paper>
  );
};

export default CategoryFilterBar;

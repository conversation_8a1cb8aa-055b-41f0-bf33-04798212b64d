import { useState, useEffect } from "react";
import {
  <PERSON>,
  Button,
  Chip,
  Menu,
  MenuItem,
  Typography,
  useMediaQuery,
  useTheme,
  Paper,
} from "@mui/material";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import SortIcon from "@mui/icons-material/Sort";
import LocalOfferIcon from "@mui/icons-material/LocalOffer";
import LocationOnIcon from "@mui/icons-material/LocationOn";

/**
 * CategoryFilterBar component that displays filter options: Sort by, Discount Items, Item Location
 * @param {Object} props - Component props
 * @param {Function} props.onCategorySelect - Callback function when a filter is selected
 */
const CategoryFilterBar = ({ onCategorySelect }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  // State for filters
  const [selectedSort, setSelectedSort] = useState(null);
  const [showDiscountItems, setShowDiscountItems] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState(null);

  // State for menu anchors
  const [sortAnchorEl, setSortAnchorEl] = useState(null);
  const [locationAnchorEl, setLocationAnchorEl] = useState(null);

  // Apply filters to a given set of ads
  const applyFiltersToAds = (ads) => {
    let filteredAds = [...ads];

    // Apply discount filter
    if (showDiscountItems) {
      filteredAds = filteredAds.filter((ad) => {
        const discount = parseFloat(ad.discount || 0);
        return discount > 0;
      });
    }

    // Apply location filter
    if (selectedLocation) {
      filteredAds = filteredAds.filter((ad) => {
        const sellerLocation = ad.sellerInfo?.location?.formattedLocation || "";
        const shippingFrom = ad.shippingFrom || "";

        switch (selectedLocation.id) {
          case "local":
            return (
              sellerLocation.toLowerCase().includes("local") ||
              shippingFrom.toLowerCase().includes("local") ||
              sellerLocation.toLowerCase().includes("same") ||
              shippingFrom.toLowerCase().includes("same")
            );
          case "national":
            return (
              !sellerLocation.toLowerCase().includes("international") &&
              !shippingFrom.toLowerCase().includes("international")
            );
          case "international":
            return (
              sellerLocation.toLowerCase().includes("international") ||
              shippingFrom.toLowerCase().includes("international") ||
              sellerLocation.toLowerCase().includes("canada") ||
              sellerLocation.toLowerCase().includes("france") ||
              sellerLocation.toLowerCase().includes("uk") ||
              shippingFrom.toLowerCase().includes("canada") ||
              shippingFrom.toLowerCase().includes("france") ||
              shippingFrom.toLowerCase().includes("uk")
            );
          default:
            return true;
        }
      });
    }

    // Apply sorting
    if (selectedSort) {
      filteredAds.sort((a, b) => {
        const priceA = calculateEffectivePrice(a);
        const priceB = calculateEffectivePrice(b);
        const dateA = a.updatedAt || a.createdAt || 0;
        const dateB = b.updatedAt || b.createdAt || 0;

        switch (selectedSort.id) {
          case "recent":
            return dateB - dateA;
          case "price_low":
            return priceA - priceB;
          case "price_high":
            return priceB - priceA;
          default:
            return dateB - dateA;
        }
      });
    }

    // Store filtered results as search results
    localStorage.setItem("searchResults", JSON.stringify(filteredAds));
    localStorage.setItem("searchTimestamp", Date.now().toString());

    // Dispatch event to update the Home component
    document.dispatchEvent(new CustomEvent("storageUpdated"));
  };

  // Sort options
  const sortOptions = [
    { id: "recent", name: "Most recent" },
    { id: "price_low", name: "Price: low to high" },
    { id: "price_high", name: "Price: high to low" },
  ];

  // Location options
  const locationOptions = [
    { id: "local", name: "Local items" },
    { id: "national", name: "National" },
    { id: "international", name: "International" },
  ];

  // Listen for events from Home component
  useEffect(() => {
    const handleAllAdsReceived = (event) => {
      const allAds = event.detail;
      if (allAds && allAds.length > 0) {
        // Apply current filters to all ads
        applyFiltersToAds(allAds);
      }
    };

    document.addEventListener("allAdsReceived", handleAllAdsReceived);

    return () => {
      document.removeEventListener("allAdsReceived", handleAllAdsReceived);
    };
  }, [selectedSort, showDiscountItems, selectedLocation, applyFiltersToAds]);

  // Apply filters to current ads (search results or all ads)
  const applyFilters = (newFilters = {}) => {
    // Get current ads from localStorage (search results or all ads)
    const searchResults = localStorage.getItem("searchResults");
    const searchTerm = localStorage.getItem("searchTerm");

    let adsToFilter = [];

    if (searchResults && searchTerm) {
      // If there are search results, filter those
      try {
        adsToFilter = JSON.parse(searchResults);
      } catch (error) {
        console.error("Error parsing search results:", error);
        return;
      }
    } else {
      // If no search results, get all ads from the Home component
      // We'll trigger a custom event to request all ads
      document.dispatchEvent(new CustomEvent("requestAllAds"));
      return;
    }

    // Apply current filters plus any new filters
    const currentSort =
      newFilters.sort !== undefined ? newFilters.sort : selectedSort;
    const currentDiscount =
      newFilters.discount !== undefined
        ? newFilters.discount
        : showDiscountItems;
    const currentLocation =
      newFilters.location !== undefined
        ? newFilters.location
        : selectedLocation;

    let filteredAds = [...adsToFilter];

    // Apply discount filter
    if (currentDiscount) {
      filteredAds = filteredAds.filter((ad) => {
        const discount = parseFloat(ad.discount || 0);
        return discount > 0;
      });
    }

    // Apply location filter
    if (currentLocation) {
      filteredAds = filteredAds.filter((ad) => {
        const sellerLocation = ad.sellerInfo?.location?.formattedLocation || "";
        const shippingFrom = ad.shippingFrom || "";

        switch (currentLocation.id) {
          case "local":
            // Filter for local items (same country as user - for now, just check if location contains common local terms)
            return (
              sellerLocation.toLowerCase().includes("local") ||
              shippingFrom.toLowerCase().includes("local") ||
              sellerLocation.toLowerCase().includes("same") ||
              shippingFrom.toLowerCase().includes("same")
            );
          case "national":
            // Filter for national items (same country)
            return (
              !sellerLocation.toLowerCase().includes("international") &&
              !shippingFrom.toLowerCase().includes("international")
            );
          case "international":
            // Filter for international items
            return (
              sellerLocation.toLowerCase().includes("international") ||
              shippingFrom.toLowerCase().includes("international") ||
              sellerLocation.toLowerCase().includes("canada") ||
              sellerLocation.toLowerCase().includes("france") ||
              sellerLocation.toLowerCase().includes("uk") ||
              shippingFrom.toLowerCase().includes("canada") ||
              shippingFrom.toLowerCase().includes("france") ||
              shippingFrom.toLowerCase().includes("uk")
            );
          default:
            return true;
        }
      });
    }

    // Apply sorting
    if (currentSort) {
      filteredAds.sort((a, b) => {
        const priceA = calculateEffectivePrice(a);
        const priceB = calculateEffectivePrice(b);
        const dateA = a.updatedAt || a.createdAt || 0;
        const dateB = b.updatedAt || b.createdAt || 0;

        switch (currentSort.id) {
          case "recent":
            return dateB - dateA; // Most recent first
          case "price_low":
            return priceA - priceB; // Low to high
          case "price_high":
            return priceB - priceA; // High to low
          default:
            return dateB - dateA;
        }
      });
    }

    // Store filtered results
    localStorage.setItem("searchResults", JSON.stringify(filteredAds));
    localStorage.setItem("searchTimestamp", Date.now().toString());

    // Dispatch event to update the Home component
    document.dispatchEvent(new CustomEvent("storageUpdated"));
  };

  // Calculate effective price (considering discounts)
  const calculateEffectivePrice = (ad) => {
    const originalPrice = parseFloat(ad.price || 0);
    const discount = parseFloat(ad.discount || 0);

    if (discount > 0) {
      return originalPrice * (1 - discount / 100);
    }

    return originalPrice;
  };

  // Handle sort menu
  const handleSortMenuOpen = (event) => {
    setSortAnchorEl(event.currentTarget);
  };

  const handleSortMenuClose = () => {
    setSortAnchorEl(null);
  };

  const handleSortSelect = (sortOption) => {
    setSelectedSort(sortOption);
    handleSortMenuClose();

    // Apply filters with new sort option
    applyFilters({ sort: sortOption });

    // Call the callback with sort information
    if (onCategorySelect) {
      onCategorySelect({ type: "sort", value: sortOption.id });
    }
  };

  // Handle discount toggle
  const handleDiscountToggle = (event) => {
    const isChecked = event.target.checked;
    setShowDiscountItems(isChecked);

    // Apply filters with new discount setting
    applyFilters({ discount: isChecked });

    // Call the callback with discount information
    if (onCategorySelect) {
      onCategorySelect({ type: "discount", value: isChecked });
    }
  };

  // Handle location menu
  const handleLocationMenuOpen = (event) => {
    setLocationAnchorEl(event.currentTarget);
  };

  const handleLocationMenuClose = () => {
    setLocationAnchorEl(null);
  };

  const handleLocationSelect = (location) => {
    setSelectedLocation(location);
    handleLocationMenuClose();

    // Apply filters with new location setting
    applyFilters({ location: location });

    // Call the callback with location information
    if (onCategorySelect) {
      onCategorySelect({ type: "location", value: location.id });
    }
  };

  // Check if menus are open
  const isSortMenuOpen = Boolean(sortAnchorEl);
  const isLocationMenuOpen = Boolean(locationAnchorEl);

  return (
    <Paper
      elevation={0}
      sx={{
        width: "100%",
        borderRadius: 0,
        borderBottom: "1px solid",
        borderColor: "divider",
        backgroundColor: "#f8f8f8",
        overflow: "auto",
        position: "sticky",
        top: 0,
        zIndex: 900,
      }}
    >
      <Box
        sx={{
          display: "flex",
          overflowX: "auto",
          py: 0.5,
          px: 2,
          "&::-webkit-scrollbar": {
            height: "4px",
          },
          "&::-webkit-scrollbar-track": {
            backgroundColor: "transparent",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "#bdbdbd",
            borderRadius: "4px",
          },
        }}
      >
        {/* Sort By Filter */}
        <Button
          color="inherit"
          endIcon={<KeyboardArrowDownIcon />}
          onClick={handleSortMenuOpen}
          aria-controls={isSortMenuOpen ? "sort-menu" : undefined}
          aria-haspopup="true"
          aria-expanded={isSortMenuOpen ? "true" : undefined}
          startIcon={<SortIcon />}
          sx={{
            textTransform: "none",
            minWidth: "auto",
            px: 1.5,
            py: 0.5,
            fontSize: isMobile ? "0.75rem" : "0.875rem",
            whiteSpace: "nowrap",
            color: selectedSort ? "primary.main" : "text.primary",
            fontWeight: selectedSort ? 600 : 400,
            mr: 1,
          }}
        >
          Sort by
        </Button>

        <Menu
          id="sort-menu"
          anchorEl={sortAnchorEl}
          open={isSortMenuOpen}
          onClose={handleSortMenuClose}
          slotProps={{
            paper: {
              elevation: 3,
              sx: {
                maxHeight: 300,
                width: isMobile ? 200 : 250,
              },
            },
          }}
        >
          {sortOptions.map((option) => (
            <MenuItem
              key={option.id}
              onClick={() => handleSortSelect(option)}
              sx={{
                fontSize: isMobile ? "0.75rem" : "0.875rem",
                py: 0.75,
                fontWeight:
                  selectedSort && selectedSort.id === option.id ? 600 : 400,
                color:
                  selectedSort && selectedSort.id === option.id
                    ? "primary.main"
                    : "text.primary",
              }}
            >
              {option.name}
            </MenuItem>
          ))}
        </Menu>

        {/* Discount Items Filter */}
        <Button
          color="inherit"
          onClick={() =>
            handleDiscountToggle({ target: { checked: !showDiscountItems } })
          }
          startIcon={
            <LocalOfferIcon color={showDiscountItems ? "primary" : "inherit"} />
          }
          sx={{
            textTransform: "none",
            minWidth: "auto",
            px: 1.5,
            py: 0.5,
            fontSize: isMobile ? "0.75rem" : "0.875rem",
            whiteSpace: "nowrap",
            color: showDiscountItems ? "primary.main" : "text.primary",
            fontWeight: showDiscountItems ? 600 : 400,
            mr: 1,
          }}
        >
          Discount Items
        </Button>

        {/* Location Filter */}
        <Button
          color="inherit"
          endIcon={<KeyboardArrowDownIcon />}
          onClick={handleLocationMenuOpen}
          aria-controls={isLocationMenuOpen ? "location-menu" : undefined}
          aria-haspopup="true"
          aria-expanded={isLocationMenuOpen ? "true" : undefined}
          startIcon={<LocationOnIcon />}
          sx={{
            textTransform: "none",
            minWidth: "auto",
            px: 1.5,
            py: 0.5,
            fontSize: isMobile ? "0.75rem" : "0.875rem",
            whiteSpace: "nowrap",
            color: selectedLocation ? "primary.main" : "text.primary",
            fontWeight: selectedLocation ? 600 : 400,
            mr: 1,
          }}
        >
          Item Location
        </Button>

        <Menu
          id="location-menu"
          anchorEl={locationAnchorEl}
          open={isLocationMenuOpen}
          onClose={handleLocationMenuClose}
          slotProps={{
            paper: {
              elevation: 3,
              sx: {
                maxHeight: 300,
                width: isMobile ? 200 : 250,
              },
            },
          }}
        >
          {locationOptions.map((option) => (
            <MenuItem
              key={option.id}
              onClick={() => handleLocationSelect(option)}
              sx={{
                fontSize: isMobile ? "0.75rem" : "0.875rem",
                py: 0.75,
                fontWeight:
                  selectedLocation && selectedLocation.id === option.id
                    ? 600
                    : 400,
                color:
                  selectedLocation && selectedLocation.id === option.id
                    ? "primary.main"
                    : "text.primary",
              }}
            >
              {option.name}
            </MenuItem>
          ))}
        </Menu>
      </Box>

      {(selectedSort || showDiscountItems || selectedLocation) && (
        <Box
          sx={{
            px: 2,
            py: 0.5,
            display: "flex",
            alignItems: "center",
            backgroundColor: "#fff",
            flexWrap: "wrap",
          }}
        >
          <Typography
            variant="body2"
            sx={{ mr: 1, color: "text.secondary", fontSize: "0.75rem" }}
          >
            Active filters:
          </Typography>

          {/* Sort filter chip */}
          {selectedSort && (
            <Chip
              label={`Sort: ${selectedSort.name}`}
              size="small"
              onDelete={() => {
                setSelectedSort(null);
                applyFilters({ sort: null });
                if (onCategorySelect)
                  onCategorySelect({ type: "sort", value: null });
              }}
              sx={{
                height: 24,
                mr: 0.5,
                mb: 0.5,
                "& .MuiChip-label": {
                  fontSize: "0.75rem",
                  px: 1,
                },
              }}
            />
          )}

          {/* Discount filter chip */}
          {showDiscountItems && (
            <Chip
              label="Discount items only"
              size="small"
              onDelete={() => {
                setShowDiscountItems(false);
                applyFilters({ discount: false });
                if (onCategorySelect)
                  onCategorySelect({ type: "discount", value: false });
              }}
              sx={{
                height: 24,
                mr: 0.5,
                mb: 0.5,
                "& .MuiChip-label": {
                  fontSize: "0.75rem",
                  px: 1,
                },
              }}
            />
          )}

          {/* Location filter chip */}
          {selectedLocation && (
            <Chip
              label={`Location: ${selectedLocation.name}`}
              size="small"
              onDelete={() => {
                setSelectedLocation(null);
                applyFilters({ location: null });
                if (onCategorySelect)
                  onCategorySelect({ type: "location", value: null });
              }}
              sx={{
                height: 24,
                mr: 0.5,
                mb: 0.5,
                "& .MuiChip-label": {
                  fontSize: "0.75rem",
                  px: 1,
                },
              }}
            />
          )}

          {/* Clear all filters button */}
          {(selectedSort || showDiscountItems || selectedLocation) && (
            <Button
              variant="text"
              size="small"
              onClick={() => {
                setSelectedSort(null);
                setShowDiscountItems(false);
                setSelectedLocation(null);

                // Clear all filters and restore original results
                const searchTerm = localStorage.getItem("searchTerm");
                if (searchTerm) {
                  // If there's a search term, re-run the search without filters
                  document.dispatchEvent(new CustomEvent("rerunSearch"));
                } else {
                  // If no search term, show all ads
                  localStorage.removeItem("searchResults");
                  localStorage.setItem(
                    "searchTimestamp",
                    Date.now().toString()
                  );
                  document.dispatchEvent(new CustomEvent("storageUpdated"));
                }

                if (onCategorySelect)
                  onCategorySelect({ type: "clear", value: null });
              }}
              sx={{
                ml: "auto",
                fontSize: "0.75rem",
                textTransform: "none",
              }}
            >
              Clear all
            </Button>
          )}
        </Box>
      )}
    </Paper>
  );
};

export default CategoryFilterBar;

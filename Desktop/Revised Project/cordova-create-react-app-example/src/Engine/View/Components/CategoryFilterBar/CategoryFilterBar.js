import React, { useState, useEffect } from 'react';
import {
  Box,
  But<PERSON>,
  Chip,
  Divider,
  Menu,
  MenuItem,
  Typography,
  useMediaQuery,
  useTheme,
  Paper
} from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { fetchCategoriesForDropdown } from '../../../../Engine/Controller/Category/CategoryController';

/**
 * CategoryFilterBar component that displays a horizontal list of category filters
 * @param {Object} props - Component props
 * @param {Function} props.onCategorySelect - Callback function when a category is selected
 */
const CategoryFilterBar = ({ onCategorySelect }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  
  // Menu state for each category
  const [anchorElMap, setAnchorElMap] = useState({});
  
  // Fetch categories from Firebase when component mounts
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const fetchedCategories = await fetchCategoriesForDropdown();
        
        if (fetchedCategories && fetchedCategories.length > 0) {
          setCategories(fetchedCategories);
        } else {
          setError('No categories found');
        }
      } catch (err) {
        console.error('Error loading categories:', err);
        setError('Failed to load categories');
      } finally {
        setLoading(false);
      }
    };
    
    loadCategories();
  }, []);
  
  // Handle opening a category menu
  const handleMenuOpen = (event, categoryId) => {
    setAnchorElMap(prev => ({
      ...prev,
      [categoryId]: event.currentTarget
    }));
  };
  
  // Handle closing a category menu
  const handleMenuClose = (categoryId) => {
    setAnchorElMap(prev => ({
      ...prev,
      [categoryId]: null
    }));
  };
  
  // Handle selecting a subcategory
  const handleSubcategorySelect = (category, subcategory) => {
    // Close the menu
    handleMenuClose(category.id);
    
    // Set selected category
    setSelectedCategory(subcategory);
    
    // Call the callback if provided
    if (onCategorySelect) {
      onCategorySelect(subcategory);
    }
  };
  
  // Check if a menu is open
  const isMenuOpen = (categoryId) => Boolean(anchorElMap[categoryId]);
  
  return (
    <Paper 
      elevation={0} 
      sx={{ 
        width: '100%', 
        borderRadius: 0,
        borderBottom: '1px solid',
        borderColor: 'divider',
        backgroundColor: '#f8f8f8',
        overflow: 'auto',
        position: 'sticky',
        top: 0,
        zIndex: 900,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          overflowX: 'auto',
          py: 0.5,
          px: 2,
          '&::-webkit-scrollbar': {
            height: '4px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'transparent',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#bdbdbd',
            borderRadius: '4px',
          },
        }}
      >
        {loading ? (
          <Typography variant="body2" sx={{ py: 1 }}>Loading categories...</Typography>
        ) : error ? (
          <Typography variant="body2" color="error" sx={{ py: 1 }}>{error}</Typography>
        ) : (
          categories.map((category) => (
            <React.Fragment key={category.id}>
              <Button
                color="inherit"
                endIcon={<KeyboardArrowDownIcon />}
                onClick={(e) => handleMenuOpen(e, category.id)}
                aria-controls={isMenuOpen(category.id) ? `${category.id}-menu` : undefined}
                aria-haspopup="true"
                aria-expanded={isMenuOpen(category.id) ? 'true' : undefined}
                sx={{
                  textTransform: 'none',
                  minWidth: 'auto',
                  px: 1.5,
                  py: 0.5,
                  fontSize: isMobile ? '0.75rem' : '0.875rem',
                  whiteSpace: 'nowrap',
                  color: selectedCategory && selectedCategory.parentId === category.id ? 'primary.main' : 'text.primary',
                  fontWeight: selectedCategory && selectedCategory.parentId === category.id ? 600 : 400,
                }}
              >
                {category.name}
              </Button>
              
              <Menu
                id={`${category.id}-menu`}
                anchorEl={anchorElMap[category.id]}
                open={isMenuOpen(category.id)}
                onClose={() => handleMenuClose(category.id)}
                MenuListProps={{
                  'aria-labelledby': `${category.id}-button`,
                }}
                PaperProps={{
                  elevation: 3,
                  sx: {
                    maxHeight: 300,
                    width: isMobile ? 200 : 250,
                  }
                }}
              >
                {category.subcategories && category.subcategories.map((subcategory) => (
                  <MenuItem 
                    key={subcategory.name}
                    onClick={() => handleSubcategorySelect(category, subcategory)}
                    sx={{
                      fontSize: isMobile ? '0.75rem' : '0.875rem',
                      py: 0.75,
                      fontWeight: selectedCategory && selectedCategory.name === subcategory.name ? 600 : 400,
                      color: selectedCategory && selectedCategory.name === subcategory.name ? 'primary.main' : 'text.primary',
                    }}
                  >
                    {subcategory.name}
                  </MenuItem>
                ))}
              </Menu>
            </React.Fragment>
          ))
        )}
      </Box>
      
      {selectedCategory && (
        <Box sx={{ px: 2, py: 0.5, display: 'flex', alignItems: 'center', backgroundColor: '#fff' }}>
          <Typography variant="body2" sx={{ mr: 1, color: 'text.secondary', fontSize: '0.75rem' }}>
            Active filter:
          </Typography>
          <Chip 
            label={selectedCategory.name} 
            size="small"
            onDelete={() => {
              setSelectedCategory(null);
              if (onCategorySelect) onCategorySelect(null);
            }}
            sx={{ 
              height: 24,
              '& .MuiChip-label': {
                fontSize: '0.75rem',
                px: 1,
              }
            }}
          />
        </Box>
      )}
    </Paper>
  );
};

export default CategoryFilterBar;

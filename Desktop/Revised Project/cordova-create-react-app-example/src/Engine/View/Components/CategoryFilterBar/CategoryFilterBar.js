import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Chip,
  Divider,
  <PERSON>u,
  MenuItem,
  Typography,
  useMediaQuery,
  useTheme,
  Paper,
  FormControlLabel,
  Switch,
  Badge,
} from "@mui/material";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import SortIcon from "@mui/icons-material/Sort";
import LocalOfferIcon from "@mui/icons-material/LocalOffer";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import { fetchCategoriesForDropdown } from "../../../../Engine/Controller/Category/CategoryController";

/**
 * CategoryFilterBar component that displays a horizontal list of category filters
 * @param {Object} props - Component props
 * @param {Function} props.onCategorySelect - Callback function when a category is selected
 */
const CategoryFilterBar = ({ onCategorySelect }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.down("md"));

  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);

  // Sort options
  const [sortAnchorEl, setSortAnchorEl] = useState(null);
  const [selectedSort, setSelectedSort] = useState(null);
  const sortOptions = [
    { id: "recent", name: "Most recent" },
    { id: "price_low", name: "Price: low to high" },
    { id: "price_high", name: "Price: high to low" },
  ];

  // Discount filter
  const [showDiscountItems, setShowDiscountItems] = useState(false);

  // Location filter
  const [locationAnchorEl, setLocationAnchorEl] = useState(null);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const locationOptions = [
    { id: "local", name: "Local items" },
    { id: "national", name: "National" },
    { id: "international", name: "International" },
  ];

  // Menu state for each category
  const [anchorElMap, setAnchorElMap] = useState({});

  // Fetch categories from Firebase when component mounts
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setLoading(true);
        setError(null);

        const fetchedCategories = await fetchCategoriesForDropdown();

        if (fetchedCategories && fetchedCategories.length > 0) {
          setCategories(fetchedCategories);
        } else {
          setError("No categories found");
        }
      } catch (err) {
        console.error("Error loading categories:", err);
        setError("Failed to load categories");
      } finally {
        setLoading(false);
      }
    };

    loadCategories();
  }, []);

  // Handle opening a category menu
  const handleMenuOpen = (event, categoryId) => {
    setAnchorElMap((prev) => ({
      ...prev,
      [categoryId]: event.currentTarget,
    }));
  };

  // Handle closing a category menu
  const handleMenuClose = (categoryId) => {
    setAnchorElMap((prev) => ({
      ...prev,
      [categoryId]: null,
    }));
  };

  // Handle selecting a subcategory
  const handleSubcategorySelect = (category, subcategory) => {
    // Close the menu
    handleMenuClose(category.id);

    // Set selected category
    setSelectedCategory(subcategory);

    // Call the callback if provided
    if (onCategorySelect) {
      onCategorySelect(subcategory);
    }
  };

  // Handle sort menu
  const handleSortMenuOpen = (event) => {
    setSortAnchorEl(event.currentTarget);
  };

  const handleSortMenuClose = () => {
    setSortAnchorEl(null);
  };

  const handleSortSelect = (sortOption) => {
    setSelectedSort(sortOption);
    handleSortMenuClose();

    // Call the callback with sort information
    if (onCategorySelect) {
      onCategorySelect({ type: "sort", value: sortOption.id });
    }
  };

  // Handle discount toggle
  const handleDiscountToggle = (event) => {
    const isChecked = event.target.checked;
    setShowDiscountItems(isChecked);

    // Call the callback with discount information
    if (onCategorySelect) {
      onCategorySelect({ type: "discount", value: isChecked });
    }
  };

  // Handle location menu
  const handleLocationMenuOpen = (event) => {
    setLocationAnchorEl(event.currentTarget);
  };

  const handleLocationMenuClose = () => {
    setLocationAnchorEl(null);
  };

  const handleLocationSelect = (location) => {
    setSelectedLocation(location);
    handleLocationMenuClose();

    // Call the callback with location information
    if (onCategorySelect) {
      onCategorySelect({ type: "location", value: location.id });
    }
  };

  // Check if menus are open
  const isMenuOpen = (categoryId) => Boolean(anchorElMap[categoryId]);
  const isSortMenuOpen = Boolean(sortAnchorEl);
  const isLocationMenuOpen = Boolean(locationAnchorEl);

  return (
    <Paper
      elevation={0}
      sx={{
        width: "100%",
        borderRadius: 0,
        borderBottom: "1px solid",
        borderColor: "divider",
        backgroundColor: "#f8f8f8",
        overflow: "auto",
        position: "sticky",
        top: 0,
        zIndex: 900,
      }}
    >
      <Box
        sx={{
          display: "flex",
          overflowX: "auto",
          py: 0.5,
          px: 2,
          "&::-webkit-scrollbar": {
            height: "4px",
          },
          "&::-webkit-scrollbar-track": {
            backgroundColor: "transparent",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "#bdbdbd",
            borderRadius: "4px",
          },
        }}
      >
        {/* Sort By Filter */}
        <Button
          color="inherit"
          endIcon={<KeyboardArrowDownIcon />}
          onClick={handleSortMenuOpen}
          aria-controls={isSortMenuOpen ? "sort-menu" : undefined}
          aria-haspopup="true"
          aria-expanded={isSortMenuOpen ? "true" : undefined}
          startIcon={<SortIcon />}
          sx={{
            textTransform: "none",
            minWidth: "auto",
            px: 1.5,
            py: 0.5,
            fontSize: isMobile ? "0.75rem" : "0.875rem",
            whiteSpace: "nowrap",
            color: selectedSort ? "primary.main" : "text.primary",
            fontWeight: selectedSort ? 600 : 400,
            mr: 1,
          }}
        >
          Sort by
        </Button>

        <Menu
          id="sort-menu"
          anchorEl={sortAnchorEl}
          open={isSortMenuOpen}
          onClose={handleSortMenuClose}
          slotProps={{
            paper: {
              elevation: 3,
              sx: {
                maxHeight: 300,
                width: isMobile ? 200 : 250,
              },
            },
          }}
        >
          {sortOptions.map((option) => (
            <MenuItem
              key={option.id}
              onClick={() => handleSortSelect(option)}
              sx={{
                fontSize: isMobile ? "0.75rem" : "0.875rem",
                py: 0.75,
                fontWeight:
                  selectedSort && selectedSort.id === option.id ? 600 : 400,
                color:
                  selectedSort && selectedSort.id === option.id
                    ? "primary.main"
                    : "text.primary",
              }}
            >
              {option.name}
            </MenuItem>
          ))}
        </Menu>

        {/* Discount Items Filter */}
        <Button
          color="inherit"
          onClick={() =>
            handleDiscountToggle({ target: { checked: !showDiscountItems } })
          }
          startIcon={
            <LocalOfferIcon color={showDiscountItems ? "primary" : "inherit"} />
          }
          sx={{
            textTransform: "none",
            minWidth: "auto",
            px: 1.5,
            py: 0.5,
            fontSize: isMobile ? "0.75rem" : "0.875rem",
            whiteSpace: "nowrap",
            color: showDiscountItems ? "primary.main" : "text.primary",
            fontWeight: showDiscountItems ? 600 : 400,
            mr: 1,
          }}
        >
          Discount Items
        </Button>

        {/* Location Filter */}
        <Button
          color="inherit"
          endIcon={<KeyboardArrowDownIcon />}
          onClick={handleLocationMenuOpen}
          aria-controls={isLocationMenuOpen ? "location-menu" : undefined}
          aria-haspopup="true"
          aria-expanded={isLocationMenuOpen ? "true" : undefined}
          startIcon={<LocationOnIcon />}
          sx={{
            textTransform: "none",
            minWidth: "auto",
            px: 1.5,
            py: 0.5,
            fontSize: isMobile ? "0.75rem" : "0.875rem",
            whiteSpace: "nowrap",
            color: selectedLocation ? "primary.main" : "text.primary",
            fontWeight: selectedLocation ? 600 : 400,
            mr: 1,
          }}
        >
          Item Location
        </Button>

        <Menu
          id="location-menu"
          anchorEl={locationAnchorEl}
          open={isLocationMenuOpen}
          onClose={handleLocationMenuClose}
          slotProps={{
            paper: {
              elevation: 3,
              sx: {
                maxHeight: 300,
                width: isMobile ? 200 : 250,
              },
            },
          }}
        >
          {locationOptions.map((option) => (
            <MenuItem
              key={option.id}
              onClick={() => handleLocationSelect(option)}
              sx={{
                fontSize: isMobile ? "0.75rem" : "0.875rem",
                py: 0.75,
                fontWeight:
                  selectedLocation && selectedLocation.id === option.id
                    ? 600
                    : 400,
                color:
                  selectedLocation && selectedLocation.id === option.id
                    ? "primary.main"
                    : "text.primary",
              }}
            >
              {option.name}
            </MenuItem>
          ))}
        </Menu>

        <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

        {/* Categories */}
        {loading ? (
          <Typography variant="body2" sx={{ py: 1 }}>
            Loading categories...
          </Typography>
        ) : error ? (
          <Typography variant="body2" color="error" sx={{ py: 1 }}>
            {error}
          </Typography>
        ) : (
          categories.map((category) => (
            <React.Fragment key={category.id}>
              <Button
                color="inherit"
                endIcon={<KeyboardArrowDownIcon />}
                onClick={(e) => handleMenuOpen(e, category.id)}
                aria-controls={
                  isMenuOpen(category.id) ? `${category.id}-menu` : undefined
                }
                aria-haspopup="true"
                aria-expanded={isMenuOpen(category.id) ? "true" : undefined}
                sx={{
                  textTransform: "none",
                  minWidth: "auto",
                  px: 1.5,
                  py: 0.5,
                  fontSize: isMobile ? "0.75rem" : "0.875rem",
                  whiteSpace: "nowrap",
                  color:
                    selectedCategory &&
                    selectedCategory.parentId === category.id
                      ? "primary.main"
                      : "text.primary",
                  fontWeight:
                    selectedCategory &&
                    selectedCategory.parentId === category.id
                      ? 600
                      : 400,
                  mr: 1,
                }}
              >
                {category.name}
              </Button>

              <Menu
                id={`${category.id}-menu`}
                anchorEl={anchorElMap[category.id]}
                open={isMenuOpen(category.id)}
                onClose={() => handleMenuClose(category.id)}
                slotProps={{
                  paper: {
                    elevation: 3,
                    sx: {
                      maxHeight: 300,
                      width: isMobile ? 200 : 250,
                    },
                  },
                }}
              >
                {category.subcategories &&
                  category.subcategories.map((subcategory) => (
                    <MenuItem
                      key={subcategory.name}
                      onClick={() =>
                        handleSubcategorySelect(category, subcategory)
                      }
                      sx={{
                        fontSize: isMobile ? "0.75rem" : "0.875rem",
                        py: 0.75,
                        fontWeight:
                          selectedCategory &&
                          selectedCategory.name === subcategory.name
                            ? 600
                            : 400,
                        color:
                          selectedCategory &&
                          selectedCategory.name === subcategory.name
                            ? "primary.main"
                            : "text.primary",
                      }}
                    >
                      {subcategory.name}
                    </MenuItem>
                  ))}
              </Menu>
            </React.Fragment>
          ))
        )}
      </Box>

      {(selectedCategory ||
        selectedSort ||
        showDiscountItems ||
        selectedLocation) && (
        <Box
          sx={{
            px: 2,
            py: 0.5,
            display: "flex",
            alignItems: "center",
            backgroundColor: "#fff",
            flexWrap: "wrap",
          }}
        >
          <Typography
            variant="body2"
            sx={{ mr: 1, color: "text.secondary", fontSize: "0.75rem" }}
          >
            Active filters:
          </Typography>

          {/* Category filter chip */}
          {selectedCategory && (
            <Chip
              label={selectedCategory.name}
              size="small"
              onDelete={() => {
                setSelectedCategory(null);
                if (onCategorySelect)
                  onCategorySelect({ type: "category", value: null });
              }}
              sx={{
                height: 24,
                mr: 0.5,
                mb: 0.5,
                "& .MuiChip-label": {
                  fontSize: "0.75rem",
                  px: 1,
                },
              }}
            />
          )}

          {/* Sort filter chip */}
          {selectedSort && (
            <Chip
              label={`Sort: ${selectedSort.name}`}
              size="small"
              onDelete={() => {
                setSelectedSort(null);
                if (onCategorySelect)
                  onCategorySelect({ type: "sort", value: null });
              }}
              sx={{
                height: 24,
                mr: 0.5,
                mb: 0.5,
                "& .MuiChip-label": {
                  fontSize: "0.75rem",
                  px: 1,
                },
              }}
            />
          )}

          {/* Discount filter chip */}
          {showDiscountItems && (
            <Chip
              label="Discount items only"
              size="small"
              onDelete={() => {
                setShowDiscountItems(false);
                if (onCategorySelect)
                  onCategorySelect({ type: "discount", value: false });
              }}
              sx={{
                height: 24,
                mr: 0.5,
                mb: 0.5,
                "& .MuiChip-label": {
                  fontSize: "0.75rem",
                  px: 1,
                },
              }}
            />
          )}

          {/* Location filter chip */}
          {selectedLocation && (
            <Chip
              label={`Location: ${selectedLocation.name}`}
              size="small"
              onDelete={() => {
                setSelectedLocation(null);
                if (onCategorySelect)
                  onCategorySelect({ type: "location", value: null });
              }}
              sx={{
                height: 24,
                mr: 0.5,
                mb: 0.5,
                "& .MuiChip-label": {
                  fontSize: "0.75rem",
                  px: 1,
                },
              }}
            />
          )}

          {/* Clear all filters button */}
          {(selectedCategory ||
            selectedSort ||
            showDiscountItems ||
            selectedLocation) && (
            <Button
              variant="text"
              size="small"
              onClick={() => {
                setSelectedCategory(null);
                setSelectedSort(null);
                setShowDiscountItems(false);
                setSelectedLocation(null);
                if (onCategorySelect)
                  onCategorySelect({ type: "clear", value: null });
              }}
              sx={{
                ml: "auto",
                fontSize: "0.75rem",
                textTransform: "none",
              }}
            >
              Clear all
            </Button>
          )}
        </Box>
      )}
    </Paper>
  );
};

export default CategoryFilterBar;

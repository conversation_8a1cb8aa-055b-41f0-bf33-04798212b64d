/**
 * CategoryView.js
 * Component for displaying category-filtered ads
 */
import React, { useState, useEffect } from "react";
import { Box, Typography, Chip, Breadcrumbs, Link } from "@mui/material";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import { storeSelectedCategory } from "../../../Controller/Category/CategoryController";
import { getCategoriesTree } from "../../../Controller/CategoriesName/CategoriesData";

const CategoryView = ({ selectedCategory, onClearCategory }) => {
  const [categoryHierarchy, setCategoryHierarchy] = useState(null);
  const [parentCategory, setParentCategory] = useState(null);

  // Load category hierarchy information
  useEffect(() => {
    const loadCategoryHierarchy = async () => {
      try {
        const categoryTree = await getCategoriesTree();
        setCategoryHierarchy(categoryTree);

        // If we have a selected category, find its parent
        if (selectedCategory && categoryTree) {
          // Create a map of subcategories to their parent categories
          const subcategoryToParent = {};
          categoryTree.forEach((parentCat) => {
            if (parentCat.subcategories) {
              parentCat.subcategories.forEach((subcat) => {
                if (subcat.name && typeof subcat.name === "string") {
                  subcategoryToParent[subcat.name.toLowerCase()] = {
                    parent: parentCat,
                    subcategory: subcat,
                  };
                }
              });
            }
          });

          // Check if the selected category is a subcategory
          const selectedCategoryName =
            selectedCategory.name || selectedCategory;
          if (
            selectedCategoryName &&
            typeof selectedCategoryName === "string"
          ) {
            const selectedCategoryLower = selectedCategoryName.toLowerCase();
            if (subcategoryToParent[selectedCategoryLower]) {
              setParentCategory(
                subcategoryToParent[selectedCategoryLower].parent
              );
            } else {
              // Check if the selected category is a parent category
              const parentCat = categoryTree.find(
                (cat) =>
                  cat.name && cat.name.toLowerCase() === selectedCategoryLower
              );
              if (parentCat) {
                setParentCategory(null); // It's a top-level category
              }
            }
          }
        }
      } catch (err) {
        console.error("Error loading category hierarchy:", err);
      }
    };

    loadCategoryHierarchy();
  }, [selectedCategory]);

  // We don't need to load ads here anymore as Home.js is handling that

  if (!selectedCategory) {
    return null;
  }

  return (
    <Box sx={{ mt: 2 }}>
      {/* Category breadcrumb navigation */}
      <Box sx={{ mb: 2 }}>
        <Breadcrumbs
          separator={<NavigateNextIcon fontSize="small" />}
          aria-label="category breadcrumb"
        >
          <Link
            color="inherit"
            component="button"
            variant="body2"
            onClick={onClearCategory}
            sx={{ textDecoration: "none" }}
          >
            All Categories
          </Link>
          {parentCategory && (
            <Link
              color="inherit"
              component="button"
              variant="body2"
              onClick={() => {
                // Handle click on parent category
                const parentCategoryObj = {
                  name: parentCategory.name,
                  id: parentCategory.id,
                };
                // Store the selected category
                storeSelectedCategory(parentCategoryObj);
              }}
              sx={{ textDecoration: "none" }}
            >
              {parentCategory.name}
            </Link>
          )}
          <Typography color="text.primary">{selectedCategory.name}</Typography>
        </Breadcrumbs>
      </Box>

      {/* Category header */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Box>
          <Typography variant="h6">{selectedCategory.name}</Typography>
          {parentCategory && (
            <Chip
              size="small"
              label={`Parent: ${parentCategory.name}`}
              variant="outlined"
              color="primary"
              sx={{ mr: 1, mt: 0.5 }}
            />
          )}
        </Box>
        <Typography
          variant="body2"
          color="primary"
          sx={{ cursor: "pointer" }}
          onClick={onClearCategory}
        >
          Clear Category
        </Typography>
      </Box>
    </Box>
  );
};

export default CategoryView;

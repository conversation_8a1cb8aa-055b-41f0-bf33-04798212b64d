import React from "react";
import { Box } from "@mui/material";
import CategoryFilterBar from "../CategoryFilterBar/CategoryFilterBar";

import { storeSelectedCategory } from "../../../Controller/Category/CategoryController";

/**
 * FilterNavigation component that displays the category filter bar
 * @param {Object} props - Component props
 */
const FilterNavigation = () => {
  console.log("🔧 FilterNavigation: Component mounting/rendering");

  // Handle category selection
  const handleCategorySelect = (category) => {
    // Only handle actual category selections, not filter selections
    if (category && category.type && category.type !== "category") {
      // This is a filter selection (location, sort, discount), not a category
      // Don't treat it as a category selection
      console.log("FilterNavigation: Ignoring filter selection:", category);
      return;
    }

    // This is an actual category selection
    console.log("FilterNavigation: Handling category selection:", category);
    storeSelectedCategory(category);
  };

  return (
    <Box
      sx={{
        width: "100%",
        position: "sticky",
        top: 0,
        zIndex: 1000,
        boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
      }}
    >
      <CategoryFilterBar onCategorySelect={handleCategorySelect} />
    </Box>
  );
};

export default FilterNavigation;

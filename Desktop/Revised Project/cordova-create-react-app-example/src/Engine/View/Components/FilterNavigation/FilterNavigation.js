import React from "react";
import { Box } from "@mui/material";
import CategoryFilterBar from "../CategoryFilterBar/CategoryFilterBar";
import SearchFilterBar from "../SearchFilterBar/SearchFilterBar";
import { storeSelectedCategory } from "../../../Controller/Category/CategoryController";

/**
 * FilterNavigation component that combines the search bar and category filter bar
 * @param {Object} props - Component props
 * @param {Function} props.onSearch - Callback function when search is performed
 */
const FilterNavigation = ({ onSearch }) => {
  // Handle category selection
  const handleCategorySelect = (category) => {
    storeSelectedCategory(category);
  };

  return (
    <Box
      sx={{
        width: "100%",
        position: "sticky",
        top: 0,
        zIndex: 1000,
        boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
      }}
    >
      <SearchFilterBar onSearch={onSearch} />
      <CategoryFilterBar onCategorySelect={handleCategorySelect} />
    </Box>
  );
};

export default FilterNavigation;

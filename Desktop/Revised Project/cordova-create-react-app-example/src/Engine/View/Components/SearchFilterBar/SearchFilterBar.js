import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  TextField,
  InputAdornment,
  Paper,
  Typography,
  useMediaQuery,
  useTheme,
  IconButton,
  Avatar,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import PersonOutlineIcon from "@mui/icons-material/PersonOutline";
import ShoppingCartOutlinedIcon from "@mui/icons-material/ShoppingCartOutlined";
import SupportOutlinedIcon from "@mui/icons-material/SupportOutlined";
import LanguageIcon from "@mui/icons-material/Language";
import { performSearch } from "../../../Controller/Search/SearchController";

/**
 * SearchFilterBar component that displays a search bar with additional filter options
 * @param {Object} props - Component props
 * @param {Function} props.onSearch - Callback function when search is performed
 */
const SearchFilterBar = ({ onSearch }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);

  // Handle search submission
  const handleSearch = async () => {
    if (!searchTerm.trim()) return;

    setIsSearching(true);
    try {
      await performSearch(searchTerm, (term, results) => {
        if (onSearch) {
          onSearch(term, results);
        }
      });
    } catch (error) {
      console.error("Error performing search:", error);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle Enter key press
  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSearch();
    }
  };

  // Handle clear button click
  const handleClear = () => {
    setSearchTerm("");
    performSearch("", onSearch);
  };

  return (
    <Paper
      elevation={0}
      sx={{
        width: "100%",
        borderRadius: 0,
        borderBottom: "1px solid",
        borderColor: "divider",
        backgroundColor: "#fff",
        py: 1,
        px: 2,
      }}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* Left side - Search field */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            flexGrow: 1,
            maxWidth: "600px",
          }}
        >
          <TextField
            placeholder="axle end joint"
            variant="outlined"
            fullWidth
            size="small"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={handleKeyDown}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="action" fontSize="small" />
                </InputAdornment>
              ),
              endAdornment: searchTerm && (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="clear search"
                    onClick={handleClear}
                    edge="end"
                    size="small"
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
              sx: {
                borderRadius: "50px",
                backgroundColor: "#f5f5f5",
                "&.MuiOutlinedInput-root": {
                  "& fieldset": {
                    borderColor: "transparent",
                  },
                  "&:hover fieldset": {
                    borderColor: "rgba(0, 0, 0, 0.23)",
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: theme.palette.primary.main,
                  },
                },
              },
            }}
            sx={{
              flexGrow: 1,
            }}
          />

          <Button
            variant="contained"
            color="primary"
            onClick={handleSearch}
            disabled={isSearching || !searchTerm.trim()}
            sx={{
              borderRadius: "50px",
              minWidth: "40px",
              width: "40px",
              height: "40px",
              p: 0,
              ml: 1,
              bgcolor: "#333",
              "&:hover": {
                bgcolor: "#000",
              },
              display: { xs: "none", sm: "flex" },
            }}
          >
            <SearchIcon />
          </Button>
        </Box>

        {/* Right side - User actions */}
        <Box
          sx={{
            display: { xs: "none", md: "flex" },
            alignItems: "center",
            ml: 2,
            gap: 2,
          }}
        >
          {/* Orders & Account */}
          <Button
            color="inherit"
            startIcon={<PersonOutlineIcon />}
            sx={{
              textTransform: "none",
              fontSize: "0.875rem",
              color: "text.primary",
            }}
          >
            Orders & Account
          </Button>

          {/* Support */}
          <Button
            color="inherit"
            startIcon={<SupportOutlinedIcon />}
            sx={{
              textTransform: "none",
              fontSize: "0.875rem",
              color: "text.primary",
            }}
          >
            Support
          </Button>

          {/* Language */}
          <Button
            color="inherit"
            startIcon={
              <Avatar
                sx={{
                  width: 24,
                  height: 24,
                  bgcolor: "transparent",
                }}
              >
                <LanguageIcon sx={{ color: "green", fontSize: "1.2rem" }} />
              </Avatar>
            }
            sx={{
              textTransform: "none",
              fontSize: "0.875rem",
              color: "text.primary",
              fontWeight: "bold",
            }}
          >
            EN
          </Button>

          {/* Cart */}
          <IconButton color="inherit" aria-label="cart">
            <ShoppingCartOutlinedIcon />
          </IconButton>
        </Box>
      </Box>
    </Paper>
  );
};

export default SearchFilterBar;

import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  TextField,
  InputAdornment,
  IconButton,
  Paper,
  Typography,
  Chip,
  Divider,
  useMediaQuery,
  Collapse,
  Button,
  List,
  ListItem,
  ListItemText,
  CircularProgress,
  useTheme,
  Badge,
  FormGroup,
  FormControlLabel,
  Checkbox,
  FormControl,
  FormLabel,
  Radio,
  RadioGroup,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import FilterListIcon from "@mui/icons-material/FilterList";
import TuneIcon from "@mui/icons-material/Tune";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";

// Import the search controller
import {
  performSearch,
  clearSearchResults,
  fetchCategories,
  getSearchResults,
} from "../../../Controller/Search/SearchController";

/**
 * Responsive Search Filter Component
 *
 * A search component that adapts to different screen sizes and provides
 * filtering capabilities for published ads.
 */
const ResponsiveSearchFilter = ({ onSearchResults }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.down("md"));

  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [showFilters, setShowFilters] = useState(!isMobile);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [categories, setCategories] = useState({
    all: [],
    parents: [],
    subcategories: [],
  });
  const [isLoadingCategories, setIsLoadingCategories] = useState(true);

  // Condition filter states
  const [conditions, setConditions] = useState({
    new: false,
    newOther: false,
    remanufactured: false,
    used: false,
    forParts: false,
    notSpecified: false,
  });

  // Price filter states
  const [priceRange, setPriceRange] = useState("");
  const [priceMin, setPriceMin] = useState("");
  const [priceMax, setPriceMax] = useState("");

  // Location filter state
  const [location, setLocation] = useState("");

  const searchInputRef = useRef(null);
  const suggestionsRef = useRef(null);

  // Load initial search term, categories, and filters from localStorage
  useEffect(() => {
    // Get saved search data
    const { searchTerm, categoryIds, filters } = getSearchResults();

    if (searchTerm) {
      setSearchTerm(searchTerm);
    }

    if (categoryIds && categoryIds.length > 0) {
      setSelectedCategories(categoryIds);
    }

    // Load saved filters
    if (filters) {
      // Load price range filters
      if (filters.priceMin !== undefined) setPriceMin(filters.priceMin);
      if (filters.priceMax !== undefined) setPriceMax(filters.priceMax);
      if (filters.priceRange) setPriceRange(filters.priceRange);

      // Load location filter
      if (filters.location) setLocation(filters.location);

      // Load condition filters
      if (filters.conditions) {
        setConditions(filters.conditions);
      }
    }

    // Fetch categories
    setIsLoadingCategories(true);
    fetchCategories(
      (categoriesData) => {
        setCategories(categoriesData);
        setIsLoadingCategories(false);
      },
      (error) => {
        console.error("Error fetching categories:", error);
        setIsLoadingCategories(false);
      }
    );
  }, []);

  // Handle clicks outside suggestions
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target) &&
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle search input change
  const handleSearchChange = (event) => {
    const value = event.target.value;
    setSearchTerm(value);

    // Show suggestions if there's text
    if (value.trim()) {
      // Generate suggestions from our categories
      const categorySuggestions = categories.all
        .filter((cat) => cat.name.toLowerCase().includes(value.toLowerCase()))
        .map((cat) => cat.name)
        .slice(0, 5);

      setSuggestions(categorySuggestions);
      setShowSuggestions(categorySuggestions.length > 0);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // Handle search submission
  const handleSearch = async () => {
    setIsSearching(true);
    setShowSuggestions(false);

    // Prepare filters object
    const filters = {};

    // Add price range filters if provided
    if (priceMin) filters.priceMin = priceMin;
    if (priceMax) filters.priceMax = priceMax;
    if (priceRange) filters.priceRange = priceRange;

    // Add location filter if provided
    if (location) filters.location = location;

    // Add condition filters if any are selected
    const selectedConditions = Object.entries(conditions)
      .filter(([_, isSelected]) => isSelected)
      .map(([condition]) => condition);

    if (selectedConditions.length > 0) {
      filters.conditions = conditions;
    }

    try {
      await performSearch(
        searchTerm,
        (term, results) => {
          if (onSearchResults) {
            onSearchResults(term, results);
          }
        },
        selectedCategories,
        filters
      );
    } catch (error) {
      console.error("Error performing search:", error);
    } finally {
      setIsSearching(false);
    }
  };

  // Clear search
  const handleClearSearch = () => {
    setSearchTerm("");
    setShowSuggestions(false);

    // Keep category filters when clearing search term
    const currentCategories = [...selectedCategories];

    clearSearchResults();

    if (onSearchResults) {
      onSearchResults("", null);
    }

    // Restore category filters after clearing search
    if (currentCategories.length > 0) {
      setSelectedCategories(currentCategories);
      localStorage.setItem(
        "searchCategoryIds",
        JSON.stringify(currentCategories)
      );
    }
  };

  // Toggle filters visibility
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Handle category selection
  const handleCategorySelect = (categoryId) => {
    let newSelectedCategories;

    if (selectedCategories.includes(categoryId)) {
      newSelectedCategories = selectedCategories.filter(
        (id) => id !== categoryId
      );
    } else {
      newSelectedCategories = [...selectedCategories, categoryId];
    }

    setSelectedCategories(newSelectedCategories);

    // Prepare filters object
    const filters = {};

    // Add price range filters if provided
    if (priceMin) filters.priceMin = priceMin;
    if (priceMax) filters.priceMax = priceMax;
    if (priceRange) filters.priceRange = priceRange;

    // Add location filter if provided
    if (location) filters.location = location;

    // Add condition filters if any are selected
    const selectedConditions = Object.entries(conditions)
      .filter(([_, isSelected]) => isSelected)
      .map(([condition]) => condition);

    if (selectedConditions.length > 0) {
      filters.conditions = conditions;
    }

    // Perform search with updated categories and filters
    performSearch(
      searchTerm,
      (term, results) => {
        if (onSearchResults) {
          onSearchResults(term, results);
        }
      },
      newSelectedCategories,
      filters
    );
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    setSearchTerm(suggestion);
    setShowSuggestions(false);

    // Prepare filters object
    const filters = {};

    // Add price range filters if provided
    if (priceMin) filters.priceMin = priceMin;
    if (priceMax) filters.priceMax = priceMax;
    if (priceRange) filters.priceRange = priceRange;

    // Add location filter if provided
    if (location) filters.location = location;

    // Add condition filters if any are selected
    const selectedConditions = Object.entries(conditions)
      .filter(([_, isSelected]) => isSelected)
      .map(([condition]) => condition);

    if (selectedConditions.length > 0) {
      filters.conditions = conditions;
    }

    // Perform search with the selected suggestion, current category filters, and additional filters
    performSearch(
      suggestion,
      (term, results) => {
        if (onSearchResults) {
          onSearchResults(term, results);
        }
      },
      selectedCategories,
      filters
    );
  };

  return (
    <Box sx={{ width: "100%", mb: 3 }}>
      {/* Search Input */}
      <Paper
        elevation={2}
        sx={{
          p: 2,
          borderRadius: 2,
          position: "relative",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <TextField
            fullWidth
            placeholder="Search for items..."
            variant="outlined"
            value={searchTerm}
            onChange={handleSearchChange}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                handleSearch();
              }
            }}
            inputRef={searchInputRef}
            // Using slots instead of InputProps (which is deprecated)
            slots={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="primary" />
                </InputAdornment>
              ),
            }}
            // Using slotProps instead of InputProps (which is deprecated)
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    {isSearching ? (
                      <CircularProgress size={24} />
                    ) : searchTerm ? (
                      <IconButton
                        aria-label="clear search"
                        onClick={handleClearSearch}
                        edge="end"
                      >
                        <ClearIcon />
                      </IconButton>
                    ) : null}
                  </InputAdornment>
                ),
              },
            }}
            sx={{ mr: 1 }}
          />

          <Button
            variant="contained"
            color="primary"
            onClick={handleSearch}
            disabled={isSearching}
            sx={{
              height: 56,
              minWidth: isMobile ? 40 : 120,
              px: isMobile ? 1 : 2,
            }}
          >
            {isMobile ? <SearchIcon /> : "Search"}
          </Button>

          {isMobile && (
            <IconButton color="primary" onClick={toggleFilters} sx={{ ml: 1 }}>
              <FilterListIcon />
            </IconButton>
          )}
        </Box>

        {/* Search Suggestions */}
        {showSuggestions && suggestions.length > 0 && (
          <Paper
            ref={suggestionsRef}
            elevation={3}
            sx={{
              position: "absolute",
              width: "calc(100% - 32px)",
              maxHeight: 300,
              overflowY: "auto",
              zIndex: 1000,
              mt: 1,
              left: 16,
            }}
          >
            <List dense>
              {suggestions.map((suggestion, index) => (
                <ListItem
                  key={index}
                  button
                  onClick={() => handleSuggestionClick(suggestion)}
                >
                  <ListItemText primary={suggestion} />
                </ListItem>
              ))}
            </List>
          </Paper>
        )}

        {/* Filter Toggle for non-mobile */}
        {!isMobile && (
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mt: 2,
            }}
          >
            <Typography variant="body2" color="text.secondary">
              {selectedCategories.length > 0
                ? `${selectedCategories.length} filter(s) applied`
                : "No filters applied"}
            </Typography>

            <Button
              startIcon={showFilters ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              onClick={toggleFilters}
              size="small"
            >
              {showFilters ? "Hide Filters" : "Show Filters"}
            </Button>
          </Box>
        )}
      </Paper>

      {/* Filters Section */}
      <Collapse in={showFilters}>
        <Paper
          elevation={1}
          sx={{
            p: 2,
            mt: 1,
            borderRadius: 2,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
            <TuneIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="h6">Filters</Typography>
          </Box>

          <Divider sx={{ mb: 2 }} />

          {/* Condition Filter - Similar to the image */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                fontWeight: "bold",
              }}
            >
              Condition
              <ExpandMoreIcon />
            </Typography>

            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={conditions.new}
                    onChange={(e) =>
                      setConditions({ ...conditions, new: e.target.checked })
                    }
                  />
                }
                label={
                  <Typography>
                    New{" "}
                    <Typography component="span" color="text.secondary">
                      (463,702)
                    </Typography>
                  </Typography>
                }
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={conditions.newOther}
                    onChange={(e) =>
                      setConditions({
                        ...conditions,
                        newOther: e.target.checked,
                      })
                    }
                  />
                }
                label={
                  <Typography>
                    New other (see details){" "}
                    <Typography component="span" color="text.secondary">
                      (2,127)
                    </Typography>
                  </Typography>
                }
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={conditions.remanufactured}
                    onChange={(e) =>
                      setConditions({
                        ...conditions,
                        remanufactured: e.target.checked,
                      })
                    }
                  />
                }
                label={
                  <Typography>
                    Remanufactured{" "}
                    <Typography component="span" color="text.secondary">
                      (530)
                    </Typography>
                  </Typography>
                }
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={conditions.used}
                    onChange={(e) =>
                      setConditions({ ...conditions, used: e.target.checked })
                    }
                  />
                }
                label={
                  <Typography>
                    Used{" "}
                    <Typography component="span" color="text.secondary">
                      (1,168)
                    </Typography>
                  </Typography>
                }
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={conditions.forParts}
                    onChange={(e) =>
                      setConditions({
                        ...conditions,
                        forParts: e.target.checked,
                      })
                    }
                  />
                }
                label={
                  <Typography>
                    For parts or not working{" "}
                    <Typography component="span" color="text.secondary">
                      (10)
                    </Typography>
                  </Typography>
                }
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={conditions.notSpecified}
                    onChange={(e) =>
                      setConditions({
                        ...conditions,
                        notSpecified: e.target.checked,
                      })
                    }
                  />
                }
                label={
                  <Typography>
                    Not specified{" "}
                    <Typography component="span" color="text.secondary">
                      (19)
                    </Typography>
                  </Typography>
                }
              />
            </FormGroup>

            <Button
              sx={{ mt: 1, color: "primary.main", textTransform: "none" }}
              onClick={handleSearch}
            >
              see all
            </Button>
          </Box>

          <Divider sx={{ mb: 2 }} />

          {/* Price Filter - Similar to the image */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                fontWeight: "bold",
              }}
            >
              Price
              <ExpandMoreIcon />
            </Typography>

            <FormGroup>
              <FormControlLabel
                control={
                  <Radio
                    checked={priceRange === "under30"}
                    onChange={() => {
                      setPriceRange("under30");
                      setPriceMin("");
                      setPriceMax("30.00");
                    }}
                    name="price-radio"
                  />
                }
                label="Under EUR 30.00"
              />
              <FormControlLabel
                control={
                  <Radio
                    checked={priceRange === "30to65"}
                    onChange={() => {
                      setPriceRange("30to65");
                      setPriceMin("30.00");
                      setPriceMax("65.00");
                    }}
                    name="price-radio"
                  />
                }
                label="EUR 30.00 to EUR 65.00"
              />
              <FormControlLabel
                control={
                  <Radio
                    checked={priceRange === "over65"}
                    onChange={() => {
                      setPriceRange("over65");
                      setPriceMin("65.00");
                      setPriceMax("");
                    }}
                    name="price-radio"
                  />
                }
                label="Over EUR 65.00"
              />
              <FormControlLabel
                control={
                  <Radio
                    checked={priceRange === "custom"}
                    onChange={() => {
                      setPriceRange("custom");
                    }}
                    name="price-radio"
                  />
                }
                label="Custom range"
              />
            </FormGroup>

            {priceRange === "custom" && (
              <Box
                sx={{ display: "flex", alignItems: "center", mt: 2, gap: 1 }}
              >
                <Box
                  sx={{
                    border: "1px solid #ccc",
                    borderRadius: 1,
                    p: 1,
                    width: "45%",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Typography sx={{ mr: 1 }}>€</Typography>
                  <TextField
                    placeholder="Min."
                    variant="standard"
                    fullWidth
                    value={priceMin}
                    onChange={(e) => setPriceMin(e.target.value)}
                    // Using slotProps instead of deprecated InputProps
                    slotProps={{ input: { disableUnderline: true } }}
                  />
                </Box>

                <Typography>to</Typography>

                <Box
                  sx={{
                    border: "1px solid #ccc",
                    borderRadius: 1,
                    p: 1,
                    width: "45%",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Typography sx={{ mr: 1 }}>€</Typography>
                  <TextField
                    placeholder="Max."
                    variant="standard"
                    fullWidth
                    value={priceMax}
                    onChange={(e) => setPriceMax(e.target.value)}
                    // Using slotProps instead of deprecated InputProps
                    slotProps={{ input: { disableUnderline: true } }}
                  />
                </Box>

                <IconButton
                  sx={{
                    bgcolor: "#f5f5f5",
                    borderRadius: "50%",
                    "&:hover": { bgcolor: "#e0e0e0" },
                  }}
                  onClick={handleSearch}
                >
                  <ArrowForwardIcon />
                </IconButton>
              </Box>
            )}
          </Box>

          <Divider sx={{ mb: 2 }} />

          {/* Location Filter */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                fontWeight: "bold",
              }}
            >
              Location
              <ExpandMoreIcon />
            </Typography>

            <Box sx={{ display: "flex", alignItems: "center", mt: 1 }}>
              <TextField
                fullWidth
                placeholder="Enter location..."
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                // Using slots and slotProps instead of deprecated InputProps
                slots={{
                  startAdornment: () => (
                    <InputAdornment position="start">
                      <LocationOnIcon color="action" />
                    </InputAdornment>
                  ),
                }}
                size="small"
                sx={{ mb: 1 }}
              />
            </Box>
          </Box>

          {/* Categories Section */}
          <Divider sx={{ mb: 2 }} />

          <Box sx={{ mb: 3 }}>
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                fontWeight: "bold",
              }}
            >
              Categories
              <ExpandMoreIcon />
            </Typography>

            {isLoadingCategories ? (
              <Box sx={{ display: "flex", justifyContent: "center", my: 2 }}>
                <CircularProgress size={24} />
              </Box>
            ) : (
              <>
                {/* Parent Categories */}
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    gap: 1,
                    mb: 2,
                  }}
                >
                  {categories.parents.map((category) => (
                    <FormControlLabel
                      key={category.id}
                      control={
                        <Checkbox
                          checked={selectedCategories.includes(category.id)}
                          onChange={() => handleCategorySelect(category.id)}
                        />
                      }
                      label={category.name}
                    />
                  ))}
                </Box>

                {/* Subcategories (if needed) */}
                {categories.parents.some(
                  (parent) =>
                    selectedCategories.includes(parent.id) &&
                    parent.subcategories &&
                    parent.subcategories.length > 0
                ) && (
                  <Box sx={{ pl: 3 }}>
                    {categories.parents
                      .filter((parent) =>
                        selectedCategories.includes(parent.id)
                      )
                      .flatMap((parent) => parent.subcategories || [])
                      .map((subcategory) => (
                        <FormControlLabel
                          key={subcategory.id}
                          control={
                            <Checkbox
                              checked={selectedCategories.includes(
                                subcategory.id
                              )}
                              onChange={() =>
                                handleCategorySelect(subcategory.id)
                              }
                              size="small"
                            />
                          }
                          label={subcategory.name}
                        />
                      ))}
                  </Box>
                )}
              </>
            )}
          </Box>

          {/* Apply Filters Button */}
          <Button
            variant="contained"
            color="primary"
            onClick={handleSearch}
            sx={{ mt: 1, mb: 2, width: "100%" }}
          >
            Apply Filters
          </Button>

          {/* Clear Filters Button */}
          {(selectedCategories.length > 0 ||
            priceMin ||
            priceMax ||
            priceRange ||
            location ||
            Object.values(conditions).some((value) => value)) && (
            <Button
              variant="outlined"
              size="small"
              onClick={() => {
                // Clear all filters
                setSelectedCategories([]);
                setPriceMin("");
                setPriceMax("");
                setPriceRange("");
                setLocation("");
                setConditions({
                  new: false,
                  newOther: false,
                  remanufactured: false,
                  used: false,
                  forParts: false,
                  notSpecified: false,
                });

                // Perform search with cleared filters
                performSearch(
                  searchTerm,
                  (term, results) => {
                    if (onSearchResults) {
                      onSearchResults(term, results);
                    }
                  },
                  [],
                  {}
                );
              }}
              sx={{ mt: 1 }}
            >
              Clear All Filters
            </Button>
          )}
        </Paper>
      </Collapse>
    </Box>
  );
};

export default ResponsiveSearchFilter;

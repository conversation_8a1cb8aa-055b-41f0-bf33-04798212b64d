import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  TextField,
  InputAdornment,
  IconButton,
  Paper,
  Typography,
  Chip,
  Divider,
  useMediaQuery,
  Collapse,
  Button,
  List,
  ListItem,
  ListItemText,
  CircularProgress,
  useTheme,
  Badge,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import FilterListIcon from "@mui/icons-material/FilterList";
import TuneIcon from "@mui/icons-material/Tune";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";

// Import the search controller
import {
  performSearch,
  clearSearchResults,
  fetchCategories,
  getSearchResults,
} from "../../../Controller/Search/SearchController";

/**
 * Responsive Search Filter Component
 *
 * A search component that adapts to different screen sizes and provides
 * filtering capabilities for published ads.
 */
const ResponsiveSearchFilter = ({ onSearchResults }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.down("md"));

  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [showFilters, setShowFilters] = useState(!isMobile);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [categories, setCategories] = useState({
    all: [],
    parents: [],
    subcategories: [],
  });
  const [isLoadingCategories, setIsLoadingCategories] = useState(true);
  const searchInputRef = useRef(null);
  const suggestionsRef = useRef(null);

  // Load initial search term and categories from localStorage
  useEffect(() => {
    // Get saved search data
    const { searchTerm, categoryIds } = getSearchResults();

    if (searchTerm) {
      setSearchTerm(searchTerm);
    }

    if (categoryIds && categoryIds.length > 0) {
      setSelectedCategories(categoryIds);
    }

    // Fetch categories
    setIsLoadingCategories(true);
    fetchCategories(
      (categoriesData) => {
        setCategories(categoriesData);
        setIsLoadingCategories(false);
      },
      (error) => {
        console.error("Error fetching categories:", error);
        setIsLoadingCategories(false);
      }
    );
  }, []);

  // Handle clicks outside suggestions
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target) &&
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle search input change
  const handleSearchChange = (event) => {
    const value = event.target.value;
    setSearchTerm(value);

    // Show suggestions if there's text
    if (value.trim()) {
      // Generate suggestions from our categories
      const categorySuggestions = categories.all
        .filter((cat) => cat.name.toLowerCase().includes(value.toLowerCase()))
        .map((cat) => cat.name)
        .slice(0, 5);

      setSuggestions(categorySuggestions);
      setShowSuggestions(categorySuggestions.length > 0);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // Handle search submission
  const handleSearch = async () => {
    setIsSearching(true);
    setShowSuggestions(false);

    try {
      await performSearch(
        searchTerm,
        (term, results) => {
          if (onSearchResults) {
            onSearchResults(term, results);
          }
        },
        selectedCategories
      );
    } catch (error) {
      console.error("Error performing search:", error);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle Enter key press
  const handleKeyPress = (event) => {
    if (event.key === "Enter") {
      handleSearch();
    }
  };

  // Clear search
  const handleClearSearch = () => {
    setSearchTerm("");
    setShowSuggestions(false);

    // Keep category filters when clearing search term
    const currentCategories = [...selectedCategories];

    clearSearchResults();

    if (onSearchResults) {
      onSearchResults("", null);
    }

    // Restore category filters after clearing search
    if (currentCategories.length > 0) {
      setSelectedCategories(currentCategories);
      localStorage.setItem(
        "searchCategoryIds",
        JSON.stringify(currentCategories)
      );
    }
  };

  // Toggle filters visibility
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Handle category selection
  const handleCategorySelect = (categoryId) => {
    let newSelectedCategories;

    if (selectedCategories.includes(categoryId)) {
      newSelectedCategories = selectedCategories.filter(
        (id) => id !== categoryId
      );
    } else {
      newSelectedCategories = [...selectedCategories, categoryId];
    }

    setSelectedCategories(newSelectedCategories);

    // Perform search with updated categories
    performSearch(
      searchTerm,
      (term, results) => {
        if (onSearchResults) {
          onSearchResults(term, results);
        }
      },
      newSelectedCategories
    );
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    setSearchTerm(suggestion);
    setShowSuggestions(false);

    // Perform search with the selected suggestion and current category filters
    performSearch(
      suggestion,
      (term, results) => {
        if (onSearchResults) {
          onSearchResults(term, results);
        }
      },
      selectedCategories
    );
  };

  return (
    <Box sx={{ width: "100%", mb: 3 }}>
      {/* Search Input */}
      <Paper
        elevation={2}
        sx={{
          p: 2,
          borderRadius: 2,
          position: "relative",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <TextField
            fullWidth
            placeholder="Search for items..."
            variant="outlined"
            value={searchTerm}
            onChange={handleSearchChange}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                handleSearch();
              }
            }}
            inputRef={searchInputRef}
            // Using slots instead of InputProps (which is deprecated)
            slots={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="primary" />
                </InputAdornment>
              ),
            }}
            // Using slotProps instead of InputProps (which is deprecated)
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    {isSearching ? (
                      <CircularProgress size={24} />
                    ) : searchTerm ? (
                      <IconButton
                        aria-label="clear search"
                        onClick={handleClearSearch}
                        edge="end"
                      >
                        <ClearIcon />
                      </IconButton>
                    ) : null}
                  </InputAdornment>
                ),
              },
            }}
            sx={{ mr: 1 }}
          />

          <Button
            variant="contained"
            color="primary"
            onClick={handleSearch}
            disabled={isSearching}
            sx={{
              height: 56,
              minWidth: isMobile ? 40 : 120,
              px: isMobile ? 1 : 2,
            }}
          >
            {isMobile ? <SearchIcon /> : "Search"}
          </Button>

          {isMobile && (
            <IconButton color="primary" onClick={toggleFilters} sx={{ ml: 1 }}>
              <FilterListIcon />
            </IconButton>
          )}
        </Box>

        {/* Search Suggestions */}
        {showSuggestions && suggestions.length > 0 && (
          <Paper
            ref={suggestionsRef}
            elevation={3}
            sx={{
              position: "absolute",
              width: "calc(100% - 32px)",
              maxHeight: 300,
              overflowY: "auto",
              zIndex: 1000,
              mt: 1,
              left: 16,
            }}
          >
            <List dense>
              {suggestions.map((suggestion, index) => (
                <ListItem
                  key={index}
                  button
                  onClick={() => handleSuggestionClick(suggestion)}
                >
                  <ListItemText primary={suggestion} />
                </ListItem>
              ))}
            </List>
          </Paper>
        )}

        {/* Filter Toggle for non-mobile */}
        {!isMobile && (
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mt: 2,
            }}
          >
            <Typography variant="body2" color="text.secondary">
              {selectedCategories.length > 0
                ? `${selectedCategories.length} filter(s) applied`
                : "No filters applied"}
            </Typography>

            <Button
              startIcon={showFilters ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              onClick={toggleFilters}
              size="small"
            >
              {showFilters ? "Hide Filters" : "Show Filters"}
            </Button>
          </Box>
        )}
      </Paper>

      {/* Filters Section */}
      <Collapse in={showFilters}>
        <Paper
          elevation={1}
          sx={{
            p: 2,
            mt: 1,
            borderRadius: 2,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
            <TuneIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="h6">Filters</Typography>
          </Box>

          <Divider sx={{ mb: 2 }} />

          <Typography variant="subtitle2" gutterBottom>
            Categories
          </Typography>

          {isLoadingCategories ? (
            <Box sx={{ display: "flex", justifyContent: "center", my: 2 }}>
              <CircularProgress size={24} />
            </Box>
          ) : (
            <>
              {/* Parent Categories */}
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 2 }}>
                {categories.parents.map((category) => (
                  <Chip
                    key={category.id}
                    label={category.name}
                    onClick={() => handleCategorySelect(category.id)}
                    color={
                      selectedCategories.includes(category.id)
                        ? "primary"
                        : "default"
                    }
                    variant={
                      selectedCategories.includes(category.id)
                        ? "filled"
                        : "outlined"
                    }
                  />
                ))}
              </Box>

              {/* Subcategories (if needed) */}
              {categories.parents.some(
                (parent) =>
                  selectedCategories.includes(parent.id) &&
                  parent.subcategories &&
                  parent.subcategories.length > 0
              ) && (
                <>
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{ mt: 1, display: "block" }}
                  >
                    Subcategories
                  </Typography>
                  <Box
                    sx={{
                      display: "flex",
                      flexWrap: "wrap",
                      gap: 1,
                      mb: 2,
                      pl: 2,
                    }}
                  >
                    {categories.parents
                      .filter((parent) =>
                        selectedCategories.includes(parent.id)
                      )
                      .flatMap((parent) => parent.subcategories || [])
                      .map((subcategory) => (
                        <Chip
                          key={subcategory.id}
                          label={subcategory.name}
                          size="small"
                          onClick={() => handleCategorySelect(subcategory.id)}
                          color={
                            selectedCategories.includes(subcategory.id)
                              ? "primary"
                              : "default"
                          }
                          variant={
                            selectedCategories.includes(subcategory.id)
                              ? "filled"
                              : "outlined"
                          }
                        />
                      ))}
                  </Box>
                </>
              )}
            </>
          )}

          {/* Additional filters can be added here */}

          {selectedCategories.length > 0 && (
            <Button
              variant="outlined"
              size="small"
              onClick={() => {
                setSelectedCategories([]);
                performSearch(
                  searchTerm,
                  (term, results) => {
                    if (onSearchResults) {
                      onSearchResults(term, results);
                    }
                  },
                  []
                );
              }}
              sx={{ mt: 1 }}
            >
              Clear Filters
            </Button>
          )}
        </Paper>
      </Collapse>
    </Box>
  );
};

export default ResponsiveSearchFilter;

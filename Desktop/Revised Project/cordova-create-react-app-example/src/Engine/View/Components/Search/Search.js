import React, { useState, useEffect } from "react";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import CircularProgress from "@mui/material/CircularProgress";
import {
  Search as SearchContainer,
  SearchIconWrapper,
  StyledInputBase,
} from "../../../Styles/MaterialUIs/NavbarStyles";
import { performSearch } from "../../../Controller/Search/SearchController";

/**
 * Search component that handles search functionality
 * @param {Object} props - Component props
 * @param {Function} props.onSearch - Callback function when search is performed
 * @param {string} props.placeholder - Placeholder text for search input
 */
const Search = ({ onSearch, placeholder = "Search..." }) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedTerm, setDebouncedTerm] = useState("");
  const [showClearButton, setShowClearButton] = useState(false);
  const [isSearching, setIsSearching] = useState(false);

  // Restore search term from localStorage on component mount
  useEffect(() => {
    const storedSearchTerm = localStorage.getItem("searchTerm");
    if (storedSearchTerm) {
      console.log(
        "🔍 Search: Restoring search term from localStorage:",
        storedSearchTerm
      );
      setSearchTerm(storedSearchTerm);
      setDebouncedTerm(storedSearchTerm);
    }
  }, []);

  // Debounce search term to avoid too many API calls
  useEffect(() => {
    const timerId = setTimeout(() => {
      setDebouncedTerm(searchTerm);
    }, 300); // 300ms delay

    return () => {
      clearTimeout(timerId);
    };
  }, [searchTerm]);

  // Show/hide clear button based on search term
  useEffect(() => {
    setShowClearButton(searchTerm.length > 0);
  }, [searchTerm]);

  // Perform search when debounced term changes
  useEffect(() => {
    const handleSearch = async () => {
      if (debouncedTerm.trim() || debouncedTerm === "") {
        setIsSearching(debouncedTerm.trim() !== "");

        try {
          console.log("Search: Performing search for:", debouncedTerm);

          // Check if this is a restored search term that already has results
          const storedSearchTerm = localStorage.getItem("searchTerm");
          const storedSearchResults = localStorage.getItem("searchResults");

          if (
            debouncedTerm &&
            debouncedTerm === storedSearchTerm &&
            storedSearchResults
          ) {
            console.log(
              "Search: Using existing search results for restored term"
            );
            // Don't perform new search, just trigger the callback with existing results
            if (onSearch) {
              onSearch(debouncedTerm, JSON.parse(storedSearchResults));
            }
          } else {
            // Perform new search
            const isEmptySearchOnMount =
              debouncedTerm === "" && !debouncedTerm.trim();
            const options = isEmptySearchOnMount ? { clearStorage: false } : {};
            await performSearch(debouncedTerm, onSearch, [], {}, options);
          }

          console.log("Search: performSearch completed");
        } finally {
          setIsSearching(false);
        }
      }
    };

    handleSearch();
  }, [debouncedTerm, onSearch]);

  // Handle Enter key press
  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      // Force immediate search on Enter
      performSearch(searchTerm, onSearch);
    }
  };

  // Handle clear button click
  const handleClear = () => {
    setSearchTerm("");
    // Explicitly clear localStorage when user clicks clear button
    performSearch("", onSearch, [], {}, { clearStorage: true });
  };

  // Handle input change
  const handleChange = (e) => {
    setSearchTerm(e.target.value);
  };

  return (
    <SearchContainer>
      <SearchIconWrapper style={{ cursor: "pointer" }}>
        {isSearching ? (
          <CircularProgress size={20} color="inherit" />
        ) : (
          <SearchIcon />
        )}
      </SearchIconWrapper>
      <StyledInputBase
        placeholder={placeholder}
        inputProps={{ "aria-label": "search" }}
        value={searchTerm}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        endAdornment={
          showClearButton && (
            <div
              onClick={handleClear}
              style={{
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
                padding: "0 8px",
              }}
            >
              <ClearIcon fontSize="small" />
            </div>
          )
        }
      />
    </SearchContainer>
  );
};

export default Search;

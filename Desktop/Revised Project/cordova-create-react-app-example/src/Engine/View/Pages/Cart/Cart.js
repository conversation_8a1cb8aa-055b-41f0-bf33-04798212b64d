import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Paper,
  Container,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  IconButton,
  Button,
  Divider,
  Grid,
  Checkbox,
  FormControlLabel,
  Alert,
  CircularProgress,
  Chip,
} from "@mui/material";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import { useNavigate } from "react-router-dom";

// Import cart controller functions
import {
  getCartItems,
  removeFromCart,
  updateCartItemQuantity,
  clearCart,
} from "../../../Controller/Cart/CartController";

// Import currency utilities
import {
  formatPriceWithSymbol,
  DEFAULT_CURRENCY_CODE,
} from "../../../Utils/CurrencyUtils";

const Cart = () => {
  const [cartItems, setCartItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedItems, setSelectedItems] = useState({});
  const [selectAll, setSelectAll] = useState(false);
  const [loadingItems, setLoadingItems] = useState({});
  const navigate = useNavigate();

  // Load cart items on component mount
  useEffect(() => {
    loadCartItems();

    // Listen for cart updates
    const handleCartUpdate = (event) => {
      const newCartItems = event.detail.cartItems;
      setCartItems(newCartItems);

      // Only reset selections if items were added or removed (not for quantity changes)
      if (!event.detail.preserveSelections) {
        setSelectedItems({});
        setSelectAll(false);
      }
    };

    document.addEventListener("cartUpdated", handleCartUpdate);

    return () => {
      document.removeEventListener("cartUpdated", handleCartUpdate);
    };
  }, []); // Only run on mount

  // Handle selection states
  useEffect(() => {
    if (cartItems.length === 0) {
      setSelectedItems({});
      setSelectAll(false);
      return;
    }

    // Only update selections if selectAll changes or if there are new items
    const currentSelections = { ...selectedItems };
    let selectionChanged = false;

    if (selectAll) {
      cartItems.forEach((item) => {
        if (!currentSelections[item.adId]) {
          currentSelections[item.adId] = true;
          selectionChanged = true;
        }
      });
    } else {
      // Make sure all cart items have a selection state
      cartItems.forEach((item) => {
        if (currentSelections[item.adId] === undefined) {
          currentSelections[item.adId] = false;
          selectionChanged = true;
        }
      });
    }

    // Only update state if selections changed
    if (selectionChanged) {
      setSelectedItems(currentSelections);
    }
  }, [cartItems, selectAll]); // Only depend on cartItems and selectAll

  // Load cart items
  const loadCartItems = () => {
    setLoading(true);
    const items = getCartItems();
    setCartItems(items);
    setLoading(false);
  };

  // Handle remove item
  const handleRemoveItem = (adId) => {
    // Save current selections before removing item
    const currentSelections = { ...selectedItems };

    // Remove the selection for the item being removed
    delete currentSelections[adId];

    // Set loading state for this specific item
    setLoadingItems((prev) => ({ ...prev, [adId]: true }));

    // Optimistic update - immediately update the UI
    const currentCart = cartItems.filter((item) => item.adId !== adId);
    setCartItems(currentCart);

    // Update selections without the removed item
    setSelectedItems(currentSelections);

    // Update selectAll state
    const allSelected =
      currentCart.length > 0 &&
      currentCart.every((item) => currentSelections[item.adId]);
    setSelectAll(allSelected);

    // Actually remove the item from cart
    const result = removeFromCart(adId);

    if (!result.success) {
      // If there was an error, revert the optimistic update
      loadCartItems();

      // Show error message
      setQuantityMessage({
        type: "error",
        text: "Failed to remove item from cart",
      });
    }

    // Clear loading state for this item
    setLoadingItems((prev) => ({ ...prev, [adId]: false }));
  };

  // State for quantity update messages
  const [quantityMessage, setQuantityMessage] = useState(null);

  // Handle quantity change
  const handleQuantityChange = async (adId, newQuantity) => {
    if (newQuantity > 0) {
      // Save current selections before updating cart
      const currentSelections = { ...selectedItems };

      // Clear any previous messages
      setQuantityMessage(null);

      // Set loading state for this specific item
      setLoadingItems((prev) => ({ ...prev, [adId]: true }));

      // Optimistic update - immediately update the UI
      const currentCart = [...cartItems];
      const itemIndex = currentCart.findIndex((item) => item.adId === adId);

      if (itemIndex !== -1) {
        // Create a copy of the item to modify
        const updatedItem = {
          ...currentCart[itemIndex],
          quantity: newQuantity,
        };

        // Replace the item in the cart
        currentCart[itemIndex] = updatedItem;

        // Update the UI immediately
        setCartItems(currentCart);
      }

      try {
        // Update cart item quantity with database check
        const result = await updateCartItemQuantity(adId, newQuantity);

        if (result.success) {
          // Update cart items with the actual result from the server
          setCartItems(result.cartItems);

          // Restore selections
          setSelectedItems(currentSelections);

          // Check if quantity was adjusted due to stock limitations
          if (
            result.adjustedQuantity &&
            result.adjustedQuantity < newQuantity
          ) {
            setQuantityMessage({
              type: "warning",
              text: `Only ${result.adjustedQuantity} items available in stock.`,
            });
          }

          // Check if item was removed due to being out of stock
          if (result.outOfStock) {
            setQuantityMessage({
              type: "error",
              text: "Item is out of stock and has been removed from your cart.",
            });
          }
        } else {
          // Show error message
          setQuantityMessage({
            type: "error",
            text: result.message || "Failed to update quantity",
          });
        }
      } catch (error) {
        console.error("Error updating quantity:", error);
        setQuantityMessage({
          type: "error",
          text: "An error occurred while updating the quantity",
        });

        // Revert the optimistic update if there was an error
        loadCartItems();
      } finally {
        // Clear loading state for this item
        setLoadingItems((prev) => ({ ...prev, [adId]: false }));
      }
    }
  };

  // Handle clear cart
  const handleClearCart = () => {
    clearCart();
  };

  // Handle checkout
  const handleCheckout = async () => {
    // Get selected items for checkout
    const itemsToCheckout = cartItems.filter(
      (item) => selectedItems[item.adId]
    );

    if (itemsToCheckout.length === 0) {
      alert("Please select at least one item to checkout");
      return;
    }

    // Verify all quantities before checkout
    setLoading(true);

    try {
      // Verify each item's quantity
      for (const item of itemsToCheckout) {
        if (!item.quantityVerified) {
          console.log(
            `Verifying quantity for item ${item.adId} before checkout`
          );
          await updateCartItemQuantity(item.adId, item.quantity);
        }
      }

      // Get the updated cart items after verification
      const updatedCart = getCartItems();

      // Filter again to get the verified items
      const verifiedItems = updatedCart.filter(
        (item) => selectedItems[item.adId]
      );

      // Navigate to checkout page with verified items
      navigate("/checkout", { state: { items: verifiedItems } });
    } catch (error) {
      console.error("Error verifying quantities before checkout:", error);
      setQuantityMessage({
        type: "error",
        text: "Error verifying item quantities. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle item selection
  const handleSelectItem = (adId) => {
    setSelectedItems((prev) => ({
      ...prev,
      [adId]: !prev[adId],
    }));

    // Update selectAll state based on selections
    const updatedSelections = {
      ...selectedItems,
      [adId]: !selectedItems[adId],
    };

    const allSelected = cartItems.every((item) => updatedSelections[item.adId]);
    setSelectAll(allSelected);
  };

  // Handle select all
  const handleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);

    const newSelectedItems = {};
    if (newSelectAll) {
      // Select all items
      cartItems.forEach((item) => {
        newSelectedItems[item.adId] = true;
      });
    }
    setSelectedItems(newSelectedItems);
  };

  // Get number of selected items
  const getSelectedCount = () => {
    return Object.values(selectedItems).filter(Boolean).length;
  };

  // Calculate total for selected items
  const getSelectedTotal = () => {
    return cartItems
      .filter((item) => selectedItems[item.adId])
      .reduce((total, item) => {
        const itemPrice = parseFloat(item.finalPrice || item.price) || 0;
        return total + itemPrice * (item.quantity || 1);
      }, 0);
  };

  // Calculate item price
  const calculateItemPrice = (item) => {
    const price = parseFloat(item.finalPrice || item.price) || 0;
    return price * (item.quantity || 1);
  };

  // Navigate to product details
  const navigateToProduct = (adId) => {
    navigate(`/ad/${adId}`);
  };

  // Continue shopping
  const continueShopping = () => {
    navigate("/");
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 8 }}>
      <Paper elevation={3} sx={{ p: 3 }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
          <ShoppingCartIcon
            sx={{ fontSize: 40, mr: 2, color: "primary.main" }}
          />
          <Typography variant="h4" component="div">
            Your Cart
          </Typography>
          {cartItems.length > 0 && (
            <Box sx={{ ml: "auto" }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectAll}
                    onChange={handleSelectAll}
                    color="primary"
                  />
                }
                label="Select All"
              />
            </Box>
          )}
        </Box>

        {/* Loading indicator */}
        {loading && (
          <Box sx={{ display: "flex", justifyContent: "center", my: 2 }}>
            <CircularProgress />
          </Box>
        )}

        {/* Quantity update messages */}
        {quantityMessage && (
          <Alert
            severity={quantityMessage.type}
            sx={{ mb: 2 }}
            onClose={() => setQuantityMessage(null)}
          >
            {quantityMessage.text}
          </Alert>
        )}

        {cartItems.length === 0 ? (
          <Box sx={{ textAlign: "center", py: 4 }}>
            <Typography variant="body1" component="div" sx={{ mb: 2 }}>
              Your cart is currently empty.
            </Typography>

            <Typography variant="body2" component="div" color="text.secondary" sx={{ mb: 3 }}>
              Add items to your cart to see them here.
            </Typography>

            <Button
              variant="contained"
              color="primary"
              onClick={continueShopping}
            >
              Continue Shopping
            </Button>
          </Box>
        ) : (
          <>
            <List sx={{ width: "100%" }}>
              {cartItems.map((item) => (
                <React.Fragment key={item.adId}>
                  <ListItem
                    alignItems="flex-start"
                    secondaryAction={
                      <IconButton
                        edge="end"
                        onClick={() => handleRemoveItem(item.adId)}
                        disabled={loadingItems[item.adId]}
                      >
                        {loadingItems[item.adId] ? (
                          <CircularProgress size={20} />
                        ) : (
                          <DeleteIcon />
                        )}
                      </IconButton>
                    }
                  >
                    <Checkbox
                      checked={!!selectedItems[item.adId]}
                      onChange={() => handleSelectItem(item.adId)}
                      sx={{ mr: 1 }}
                      color="primary"
                      size="medium"
                      onClick={(e) => e.stopPropagation()}
                    />
                    <ListItemAvatar sx={{ mr: 2 }}>
                      <Avatar
                        variant="rounded"
                        src={
                          (Array.isArray(item.photos) &&
                            item.photos.find((photo) => photo.isCover)?.url) ||
                          (Array.isArray(item.photos) && item.photos[0]?.url) ||
                          (Array.isArray(item.photos) ? item.photos[0] : "")
                        }
                        alt={item.brandName || item.itemName}
                        sx={{ width: 80, height: 80, cursor: "pointer" }}
                        onClick={() => navigateToProduct(item.adId)}
                      />
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box component="div" sx={{ fontWeight: "bold", cursor: "pointer" }} onClick={() => navigateToProduct(item.adId)}>
                          <Typography variant="subtitle1" component="span">
                            {item.brandName || item.itemName}
                          </Typography>
                        </Box>
                      }
                      secondary={
                        <Typography component="div" variant="body2">
                          {item.selectedVariants && (
                            <Box sx={{ mt: 0.5, mb: 0.5 }}>
                              {item.selectedVariants.size && (
                                <Chip
                                  label={`Size: ${item.selectedVariants.size}`}
                                  size="small"
                                  color="primary"
                                  variant="outlined"
                                  sx={{ mr: 1, mb: 0.5 }}
                                />
                              )}
                              {item.selectedVariants.color && (
                                <Chip
                                  label={`Color: ${item.selectedVariants.color}`}
                                  size="small"
                                  color="secondary"
                                  variant="outlined"
                                  sx={{ mb: 0.5 }}
                                />
                              )}
                            </Box>
                          )}

                          <Box sx={{ display: "flex", alignItems: "center", mt: 1 }}>
                            <Typography component="span" variant="body2" color="text.primary">
                              {formatPriceWithSymbol(
                                item.finalPrice || item.price,
                                DEFAULT_CURRENCY_CODE
                              )}{" "}
                              each
                            </Typography>
                            {item.quantityVerified && (
                              <Typography component="span" variant="caption" color="success.main" sx={{ ml: 1 }}>
                                ✓ Quantity available
                              </Typography>
                            )}
                          </Box>

                          <Box sx={{ display: "flex", alignItems: "center", mt: 1 }}>
                            <IconButton
                              size="small"
                              onClick={() => handleQuantityChange(item.adId, (item.quantity || 1) - 1)}
                              disabled={loadingItems[item.adId]}
                            >
                              <RemoveIcon fontSize="small" />
                            </IconButton>

                            {loadingItems[item.adId] ? (
                              <Box sx={{ display: "flex", alignItems: "center", justifyContent: "center", width: "40px", height: "32px" }}>
                                <CircularProgress size={20} />
                              </Box>
                            ) : (
                              <input
                                type="number"
                                min="1"
                                value={item.quantity || 1}
                                onChange={(e) => {
                                  const val = parseInt(e.target.value);
                                  if (!isNaN(val) && val > 0) {
                                    handleQuantityChange(item.adId, val);
                                  }
                                }}
                                style={{
                                  textAlign: "center",
                                  width: "40px",
                                  height: "32px",
                                  border: "1px solid #ccc",
                                  borderRadius: "4px",
                                  padding: "4px",
                                }}
                              />
                            )}

                            <IconButton
                              size="small"
                              onClick={() => handleQuantityChange(item.adId, (item.quantity || 1) + 1)}
                              disabled={loadingItems[item.adId]}
                            >
                              <AddIcon fontSize="small" />
                            </IconButton>

                            <Typography component="span" variant="body1" sx={{ ml: 2, fontWeight: "bold" }}>
                              {formatPriceWithSymbol(calculateItemPrice(item), DEFAULT_CURRENCY_CODE)}
                            </Typography>
                          </Box>
                        </Typography>
                      }
                    />
                  </ListItem>
                  <Divider variant="inset" component="li" sx={{ ml: 0 }} />
                </React.Fragment>
              ))}
            </List>

            <Box sx={{ mt: 4 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Button
                    variant="outlined"
                    color="secondary"
                    onClick={handleClearCart}
                    startIcon={<DeleteIcon />}
                  >
                    Clear Cart
                  </Button>
                  <Button
                    variant="outlined"
                    sx={{ ml: 2 }}
                    onClick={continueShopping}
                  >
                    Continue Shopping
                  </Button>
                </Grid>
                <Grid
                  item
                  xs={12}
                  md={6}
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: { xs: "flex-start", md: "flex-end" },
                  }}
                >
                  <Box component="div" sx={{ mb: 1 }}>
                    <Typography variant="h6" component="span">
                      Selected: {getSelectedCount()} of {cartItems.length} items
                    </Typography>
                  </Box>
                  <Box component="div" sx={{ mb: 2 }}>
                    <Typography variant="h6" component="span">
                      Subtotal:{" "}
                      <Box component="span" sx={{ fontWeight: "bold" }}>
                        {formatPriceWithSymbol(getSelectedTotal(), DEFAULT_CURRENCY_CODE)}
                      </Box>
                    </Typography>
                  </Box>
                  <Button
                    variant="contained"
                    color="primary"
                    size="large"
                    onClick={handleCheckout}
                    disabled={getSelectedCount() === 0}
                  >
                    {getSelectedCount() === 0 ? "Select Items to Checkout" : "Proceed to Checkout"}
                  </Button>
                </Grid>
              </Grid>
            </Box>
          </>
        )}
      </Paper>
    </Container>
  );
};

export default Cart;

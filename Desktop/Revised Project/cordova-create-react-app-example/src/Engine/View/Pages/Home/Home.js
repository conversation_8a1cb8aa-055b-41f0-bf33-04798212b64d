import { useState, useEffect } from "react";
import { Container, Typography, Divider, Box } from "@mui/material";
import Footer from "../../Footer/Footer";
import AdGrid from "../../Components/AdGrid/AdGrid";
import CategoryView from "../../Components/Category/CategoryView";
import ResponsiveSearchFilter from "../../Components/Search/ResponsiveSearchFilter";
import { initHomePage } from "../../../Controller/Search/SearchController";
import {
  getSelectedCategory,
  clearSelectedCategory,
  filterAdsByCategory,
  fetchAdsByCategory,
  debugCategoriesStructure,
} from "../../../Controller/Category/CategoryController";

import "./HomeStyle.css";

function Home() {
  const [allAds, setAllAds] = useState([]);
  const [searchResults, setSearchResults] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState(
    getSelectedCategory()
  );

  const [filteredAds, setFilteredAds] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [hasRestoredData, setHasRestoredData] = useState(false);

  // Early restoration check to prevent flickering
  useEffect(() => {
    const storedFilteredResults = localStorage.getItem("searchResults");
    const storedSearchTerm = localStorage.getItem("searchTerm");

    if (storedFilteredResults) {
      try {
        const parsedResults = JSON.parse(storedFilteredResults);
        console.log(
          "🏠 Home: Early restoration of",
          parsedResults.length,
          "stored results"
        );

        if (storedSearchTerm) {
          setSearchTerm(storedSearchTerm);
          setSearchResults(parsedResults);
        } else {
          setFilteredAds(parsedResults);
        }
        setHasRestoredData(true);
        setLoading(false); // Stop loading since we have data to show
      } catch (error) {
        console.error("🏠 Home: Error in early restoration:", error);
      }
    }
  }, []);

  // Initialize home page with search listeners and ads fetching
  useEffect(() => {
    console.log("Home: useEffect running - initializing home page");

    // Check for persisted filter states on component mount
    const restoreFilterStates = () => {
      const storedSearchTerm = localStorage.getItem("searchTerm");
      const storedSearchResults = localStorage.getItem("searchResults");

      console.log("🚀 Home: Initial restoration check:", {
        hasStoredTerm: !!storedSearchTerm,
        hasStoredResults: !!storedSearchResults,
        storedTerm: storedSearchTerm,
        currentSearchTerm: searchTerm,
        currentSearchResults: searchResults ? searchResults.length : 0,
      });

      if (storedSearchTerm && storedSearchResults) {
        try {
          const parsedResults = JSON.parse(storedSearchResults);
          console.log(
            "🚀 Home: Initial restoration - setting search state:",
            storedSearchTerm,
            parsedResults.length,
            "results"
          );
          setSearchTerm(storedSearchTerm);
          setSearchResults(parsedResults);
        } catch (error) {
          console.error("🚀 Home: Error restoring search state:", error);
        }
      } else {
        console.log("🚀 Home: No initial search state to restore");
      }
    };

    // Restore filter states immediately
    restoreFilterStates();

    const cleanup = initHomePage({
      onSearchResultsChange: ({
        searchTerm: storedSearchTerm,
        searchResults: storedSearchResults,
      }) => {
        console.log("🏠 Home: onSearchResultsChange called with:", {
          storedSearchTerm,
          storedSearchResults,
          resultsLength: storedSearchResults ? storedSearchResults.length : 0,
          currentSearchTerm: searchTerm,
          currentSearchResults: searchResults ? searchResults.length : 0,
        });

        // Handle different scenarios for search and filter results
        if (
          storedSearchTerm &&
          storedSearchResults &&
          storedSearchResults.length > 0
        ) {
          // Scenario 1: Search results with search term (actual search)
          console.log(
            "🏠 Home: Setting search results:",
            storedSearchTerm,
            storedSearchResults.length,
            "items"
          );
          setSearchTerm(storedSearchTerm);
          setSearchResults(storedSearchResults);
        } else if (
          !storedSearchTerm &&
          storedSearchResults &&
          storedSearchResults.length > 0
        ) {
          // Scenario 2: Filtered results without search term (filter-only mode)
          console.log(
            "🏠 Home: Setting filtered results (no search term):",
            storedSearchResults.length,
            "items"
          );
          setSearchTerm("");
          setSearchResults(null);
          setFilteredAds(storedSearchResults);
        } else if (storedSearchTerm === "" && !storedSearchResults) {
          // Scenario 3: Explicit clear (empty search term and no results)
          console.log("🏠 Home: Clearing search results (explicit clear)");
          setSearchResults(null);
          setSearchTerm("");
        } else {
          console.log(
            "🏠 Home: Ignoring partial data - letting restoration handle it"
          );
        }
      },

      onAdsLoaded: (ads) => {
        console.log("🏠 Home: onAdsLoaded called with", ads.length, "ads");
        setAllAds(ads);

        // If we already restored data early, don't override it
        if (hasRestoredData) {
          console.log(
            "🏠 Home: Data already restored early, skipping restoration"
          );
        } else {
          // Check if we have filtered results to restore (either from search or filters)
          const storedFilteredResults = localStorage.getItem("searchResults");
          const storedSearchTerm = localStorage.getItem("searchTerm");

          console.log("🏠 Home: onAdsLoaded - checking for restoration:", {
            hasStoredResults: !!storedFilteredResults,
            hasStoredTerm: !!storedSearchTerm,
            storedTerm: storedSearchTerm,
            currentSearchTerm: searchTerm,
            currentSearchResults: searchResults ? searchResults.length : 0,
          });

          if (storedFilteredResults) {
            // If we have stored results (from search or filters), use those IMMEDIATELY
            try {
              const parsedResults = JSON.parse(storedFilteredResults);
              console.log(
                "🏠 Home: Parsed stored results:",
                parsedResults.length,
                "ads"
              );

              if (storedSearchTerm) {
                // If we have a search term, restore search results
                console.log(
                  "🏠 Home: Restoring search results for term:",
                  storedSearchTerm
                );
                setSearchTerm(storedSearchTerm);
                setSearchResults(parsedResults);
                // Don't set filteredAds to all ads - keep it empty or set to parsed results
                setFilteredAds([]); // Prevent showing all ads
              } else {
                // If no search term, restore filtered ads (from filters only)
                console.log("🏠 Home: Restoring filtered ads (no search term)");
                setFilteredAds(parsedResults);
                // Don't show all ads first
              }
            } catch (error) {
              console.error(
                "🏠 Home: Error parsing stored filtered results:",
                error
              );
              setFilteredAds(ads);
            }
          } else {
            console.log(
              "🏠 Home: No stored results, setting all ads as filtered ads"
            );
            setFilteredAds(ads);
          }
        }

        // Store all ads in localStorage for CategoryFilterBar access
        localStorage.setItem("allAds", JSON.stringify(ads));
        // Notify CategoryFilterBar that ads are available
        setTimeout(() => {
          document.dispatchEvent(
            new CustomEvent("allAdsReceived", { detail: ads })
          );
        }, 100); // Small delay to ensure CategoryFilterBar is mounted
      },

      onError: (errorMessage) => {
        setError("Error fetching ads: " + errorMessage);
      },

      onLoadingChange: (isLoading) => {
        setLoading(isLoading);
      },
    });

    const handleCategoryChange = (event) => {
      const category = event.detail.category;
      setSelectedCategory(category);
    };

    document.addEventListener("categoryChanged", handleCategoryChange);

    return () => {
      cleanup();
      document.removeEventListener("categoryChanged", handleCategoryChange);
    };
  }, []); // Remove selectedCategory dependency to prevent infinite loop

  // Separate useEffect for handling CategoryFilterBar events
  useEffect(() => {
    // Handle request for all ads from CategoryFilterBar
    const handleRequestAllAds = () => {
      if (allAds.length > 0) {
        // Store all ads in localStorage for CategoryFilterBar access
        localStorage.setItem("allAds", JSON.stringify(allAds));
        // Send all ads to CategoryFilterBar for filtering
        document.dispatchEvent(
          new CustomEvent("allAdsReceived", { detail: allAds })
        );
      }
    };

    // Handle re-run search request from CategoryFilterBar
    const handleRerunSearch = () => {
      const searchTerm = localStorage.getItem("searchTerm");
      if (searchTerm) {
        // Re-run the original search without filters
        // This will be handled by the search component
        document.dispatchEvent(
          new CustomEvent("rerunOriginalSearch", { detail: searchTerm })
        );
      }
    };

    // Handle storage updates from CategoryFilterBar (when filters are applied)
    const handleStorageUpdated = () => {
      console.log("Home: StorageUpdated event received from CategoryFilterBar");
      const filteredResults = localStorage.getItem("searchResults");
      const searchTerm = localStorage.getItem("searchTerm");

      if (filteredResults) {
        try {
          const parsedResults = JSON.parse(filteredResults);
          console.log(
            "Home: Updating display with filtered results:",
            parsedResults.length,
            "ads"
          );

          if (searchTerm) {
            // If there's a search term, update search results
            setSearchResults(parsedResults);
          } else {
            // If no search term, update filtered ads (for browsing all ads)
            setFilteredAds(parsedResults);
          }
        } catch (error) {
          console.error("Home: Error parsing filtered results:", error);
        }
      }
    };

    document.addEventListener("requestAllAds", handleRequestAllAds);
    document.addEventListener("rerunSearch", handleRerunSearch);
    document.addEventListener("storageUpdated", handleStorageUpdated);

    return () => {
      document.removeEventListener("requestAllAds", handleRequestAllAds);
      document.removeEventListener("rerunSearch", handleRerunSearch);
      document.removeEventListener("storageUpdated", handleStorageUpdated);
    };
  }, [allAds]);

  // Update filtered ads when allAds or selectedCategory changes
  useEffect(() => {
    const updateFilteredAds = async () => {
      console.log("updateFilteredAds called with:", {
        selectedCategory,
        allAdsCount: allAds.length,
      });

      // Debug categories structure
      await debugCategoriesStructure();

      if (selectedCategory) {
        setLoading(true);
        try {
          // Use fetchAdsByCategory for more accurate filtering
          console.log("Fetching ads for category:", selectedCategory);
          console.log("Category name:", selectedCategory.name);
          console.log(
            "Category supercategory:",
            selectedCategory.supercategory
          );

          // First try with fetchAdsByCategory
          const categoryAds = await fetchAdsByCategory(selectedCategory);
          console.log(
            `Found ${categoryAds.length} ads for category ${selectedCategory.name}`
          );

          // If no ads found, try with filterAdsByCategory as fallback
          if (categoryAds.length === 0) {
            console.log(
              "No ads found with fetchAdsByCategory, trying filterAdsByCategory"
            );
            const filteredAds = filterAdsByCategory(allAds, selectedCategory);
            console.log(
              `Found ${filteredAds.length} ads with filterAdsByCategory`
            );
            setFilteredAds(filteredAds);
          } else {
            setFilteredAds(categoryAds);
          }
        } catch (err) {
          console.error("Error fetching category ads:", err);
          // Fallback to client-side filtering if fetch fails
          console.log("Error occurred, falling back to filterAdsByCategory");
          const filteredAds = filterAdsByCategory(allAds, selectedCategory);
          console.log(
            `Found ${filteredAds.length} ads with filterAdsByCategory after error`
          );
          setFilteredAds(filteredAds);
        } finally {
          setLoading(false);
        }
      } else {
        console.log("No category selected, showing all ads:", allAds.length);
        setFilteredAds(allAds);
      }
    };

    updateFilteredAds();
  }, [allAds, selectedCategory]);

  const handleClearCategory = () => {
    clearSelectedCategory();
    setSelectedCategory(null);

    if (searchResults) {
      const storedResults = localStorage.getItem("searchResults");
      if (storedResults) {
        setSearchResults(JSON.parse(storedResults));
      }
    } else {
      setFilteredAds(allAds);
    }
  };

  // Handle search results from ResponsiveSearchFilter
  const handleSearchResults = (term, results) => {
    setSearchTerm(term);
    setSearchResults(results);
  };

  return (
    <div className="home-container">
      <Container className="home-content">
        {/* Responsive Search Filter */}

        {/* Search Results */}
        {searchResults !== null && (
          <>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 2,
              }}
            >
              <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
                Search Results for "{searchTerm}"
                {selectedCategory && " in category: " + selectedCategory.name}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Found {searchResults.length} result
                {searchResults.length !== 1 ? "s" : ""}
              </Typography>
            </Box>
            <AdGrid
              ads={searchResults}
              emptyMessage={`No results found for "${searchTerm}"${
                selectedCategory ? ` in category: ${selectedCategory.name}` : ""
              }. Try different keywords or categories.`}
              loading={loading}
              error={error}
            />
            <Divider sx={{ my: 3 }} />
          </>
        )}

        {/* Category Selection (if not searching) */}
        {selectedCategory && searchResults === null && (
          <>
            <CategoryView
              selectedCategory={selectedCategory}
              onClearCategory={handleClearCategory}
            />
            <AdGrid
              ads={filteredAds}
              emptyMessage={`No ads found in category "${selectedCategory.name}"`}
              loading={loading}
              error={error}
            />
          </>
        )}

        {/* All Ads Section (default view) */}
        {!selectedCategory && searchResults === null && (
          <AdGrid
            ads={filteredAds}
            title=""
            emptyMessage="No products available"
            loading={loading}
            error={error}
          />
        )}
      </Container>
      <Footer />
    </div>
  );
}

export default Home;

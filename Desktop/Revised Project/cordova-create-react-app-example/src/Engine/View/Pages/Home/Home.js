import { useState, useEffect } from "react";
import { Container, Typography, Divider, Box } from "@mui/material";
import Footer from "../../Footer/Footer";
import AdGrid from "../../Components/AdGrid/AdGrid";
import CategoryView from "../../Components/Category/CategoryView";
import ResponsiveSearchFilter from "../../Components/Search/ResponsiveSearchFilter";
import { initHomePage } from "../../../Controller/Search/SearchController";
import {
  getSelectedCategory,
  clearSelectedCategory,
  filterAdsByCategory,
  fetchAdsByCategory,
  debugCategoriesStructure,
} from "../../../Controller/Category/CategoryController";

import "./HomeStyle.css";

function Home() {
  const [allAds, setAllAds] = useState([]);
  const [searchResults, setSearchResults] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState(
    getSelectedCategory()
  );

  const [filteredAds, setFilteredAds] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Initialize home page with search listeners and ads fetching
  useEffect(() => {
    console.log("Home: useEffect running - initializing home page");

    // Check for persisted filter states on component mount
    const restoreFilterStates = () => {
      const storedSearchTerm = localStorage.getItem("searchTerm");
      const storedSearchResults = localStorage.getItem("searchResults");

      if (storedSearchTerm && storedSearchResults) {
        try {
          const parsedResults = JSON.parse(storedSearchResults);
          setSearchTerm(storedSearchTerm);
          setSearchResults(parsedResults);
          console.log(
            "Home: Restored search state:",
            storedSearchTerm,
            parsedResults.length,
            "results"
          );
        } catch (error) {
          console.error("Home: Error restoring search state:", error);
        }
      }
    };

    // Restore filter states immediately
    restoreFilterStates();

    const cleanup = initHomePage({
      onSearchResultsChange: ({
        searchTerm: storedSearchTerm,
        searchResults: storedSearchResults,
      }) => {
        console.log("Home: onSearchResultsChange called with:", {
          storedSearchTerm,
          storedSearchResults,
          resultsLength: storedSearchResults ? storedSearchResults.length : 0,
        });
        if (storedSearchTerm && storedSearchResults) {
          setSearchTerm(storedSearchTerm);
          setSearchResults(storedSearchResults);
          console.log(
            "Home: Set search results:",
            storedSearchResults.length,
            "items"
          );
        } else {
          setSearchResults(null);
          setSearchTerm("");
          console.log("Home: Cleared search results");
        }
      },

      onAdsLoaded: (ads) => {
        setAllAds(ads);

        // Check if we have filtered results to restore (either from search or filters)
        const storedFilteredResults = localStorage.getItem("searchResults");
        const storedSearchTerm = localStorage.getItem("searchTerm");

        if (storedFilteredResults) {
          // If we have stored results (from search or filters), use those
          try {
            const parsedResults = JSON.parse(storedFilteredResults);
            setFilteredAds(parsedResults);
            console.log(
              "Home: Restored filtered results:",
              parsedResults.length,
              "ads",
              storedSearchTerm
                ? `for search "${storedSearchTerm}"`
                : "from filters"
            );
          } catch (error) {
            console.error(
              "Home: Error parsing stored filtered results:",
              error
            );
            setFilteredAds(ads);
          }
        } else {
          setFilteredAds(ads);
        }

        // Store all ads in localStorage for CategoryFilterBar access
        localStorage.setItem("allAds", JSON.stringify(ads));
        // Notify CategoryFilterBar that ads are available
        setTimeout(() => {
          document.dispatchEvent(
            new CustomEvent("allAdsReceived", { detail: ads })
          );
        }, 100); // Small delay to ensure CategoryFilterBar is mounted
      },

      onError: (errorMessage) => {
        setError("Error fetching ads: " + errorMessage);
      },

      onLoadingChange: (isLoading) => {
        setLoading(isLoading);
      },
    });

    const handleCategoryChange = (event) => {
      const category = event.detail.category;
      setSelectedCategory(category);
    };

    document.addEventListener("categoryChanged", handleCategoryChange);

    return () => {
      cleanup();
      document.removeEventListener("categoryChanged", handleCategoryChange);
    };
  }, []); // Remove selectedCategory dependency to prevent infinite loop

  // Separate useEffect for handling CategoryFilterBar events
  useEffect(() => {
    // Handle request for all ads from CategoryFilterBar
    const handleRequestAllAds = () => {
      if (allAds.length > 0) {
        // Store all ads in localStorage for CategoryFilterBar access
        localStorage.setItem("allAds", JSON.stringify(allAds));
        // Send all ads to CategoryFilterBar for filtering
        document.dispatchEvent(
          new CustomEvent("allAdsReceived", { detail: allAds })
        );
      }
    };

    // Handle re-run search request from CategoryFilterBar
    const handleRerunSearch = () => {
      const searchTerm = localStorage.getItem("searchTerm");
      if (searchTerm) {
        // Re-run the original search without filters
        // This will be handled by the search component
        document.dispatchEvent(
          new CustomEvent("rerunOriginalSearch", { detail: searchTerm })
        );
      }
    };

    // Handle storage updates from CategoryFilterBar (when filters are applied)
    const handleStorageUpdated = () => {
      console.log("Home: StorageUpdated event received from CategoryFilterBar");
      const filteredResults = localStorage.getItem("searchResults");
      const searchTerm = localStorage.getItem("searchTerm");

      if (filteredResults) {
        try {
          const parsedResults = JSON.parse(filteredResults);
          console.log(
            "Home: Updating display with filtered results:",
            parsedResults.length,
            "ads"
          );

          if (searchTerm) {
            // If there's a search term, update search results
            setSearchResults(parsedResults);
          } else {
            // If no search term, update filtered ads (for browsing all ads)
            setFilteredAds(parsedResults);
          }
        } catch (error) {
          console.error("Home: Error parsing filtered results:", error);
        }
      }
    };

    document.addEventListener("requestAllAds", handleRequestAllAds);
    document.addEventListener("rerunSearch", handleRerunSearch);
    document.addEventListener("storageUpdated", handleStorageUpdated);

    return () => {
      document.removeEventListener("requestAllAds", handleRequestAllAds);
      document.removeEventListener("rerunSearch", handleRerunSearch);
      document.removeEventListener("storageUpdated", handleStorageUpdated);
    };
  }, [allAds]);

  // Update filtered ads when allAds or selectedCategory changes
  useEffect(() => {
    const updateFilteredAds = async () => {
      console.log("updateFilteredAds called with:", {
        selectedCategory,
        allAdsCount: allAds.length,
      });

      // Debug categories structure
      await debugCategoriesStructure();

      if (selectedCategory) {
        setLoading(true);
        try {
          // Use fetchAdsByCategory for more accurate filtering
          console.log("Fetching ads for category:", selectedCategory);
          console.log("Category name:", selectedCategory.name);
          console.log(
            "Category supercategory:",
            selectedCategory.supercategory
          );

          // First try with fetchAdsByCategory
          const categoryAds = await fetchAdsByCategory(selectedCategory);
          console.log(
            `Found ${categoryAds.length} ads for category ${selectedCategory.name}`
          );

          // If no ads found, try with filterAdsByCategory as fallback
          if (categoryAds.length === 0) {
            console.log(
              "No ads found with fetchAdsByCategory, trying filterAdsByCategory"
            );
            const filteredAds = filterAdsByCategory(allAds, selectedCategory);
            console.log(
              `Found ${filteredAds.length} ads with filterAdsByCategory`
            );
            setFilteredAds(filteredAds);
          } else {
            setFilteredAds(categoryAds);
          }
        } catch (err) {
          console.error("Error fetching category ads:", err);
          // Fallback to client-side filtering if fetch fails
          console.log("Error occurred, falling back to filterAdsByCategory");
          const filteredAds = filterAdsByCategory(allAds, selectedCategory);
          console.log(
            `Found ${filteredAds.length} ads with filterAdsByCategory after error`
          );
          setFilteredAds(filteredAds);
        } finally {
          setLoading(false);
        }
      } else {
        console.log("No category selected, showing all ads:", allAds.length);
        setFilteredAds(allAds);
      }
    };

    updateFilteredAds();
  }, [allAds, selectedCategory]);

  const handleClearCategory = () => {
    clearSelectedCategory();
    setSelectedCategory(null);

    if (searchResults) {
      const storedResults = localStorage.getItem("searchResults");
      if (storedResults) {
        setSearchResults(JSON.parse(storedResults));
      }
    } else {
      setFilteredAds(allAds);
    }
  };

  // Handle search results from ResponsiveSearchFilter
  const handleSearchResults = (term, results) => {
    setSearchTerm(term);
    setSearchResults(results);
  };

  return (
    <div className="home-container">
      <Container className="home-content">
        {/* Responsive Search Filter */}

        {/* Search Results */}
        {searchResults !== null && (
          <>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 2,
              }}
            >
              <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
                Search Results for "{searchTerm}"
                {selectedCategory && " in category: " + selectedCategory.name}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Found {searchResults.length} result
                {searchResults.length !== 1 ? "s" : ""}
              </Typography>
            </Box>
            <AdGrid
              ads={searchResults}
              emptyMessage={`No results found for "${searchTerm}"${
                selectedCategory ? ` in category: ${selectedCategory.name}` : ""
              }. Try different keywords or categories.`}
              loading={loading}
              error={error}
            />
            <Divider sx={{ my: 3 }} />
          </>
        )}

        {/* Category Selection (if not searching) */}
        {selectedCategory && searchResults === null && (
          <>
            <CategoryView
              selectedCategory={selectedCategory}
              onClearCategory={handleClearCategory}
            />
            <AdGrid
              ads={filteredAds}
              emptyMessage={`No ads found in category "${selectedCategory.name}"`}
              loading={loading}
              error={error}
            />
          </>
        )}

        {/* All Ads Section (default view) */}
        {!selectedCategory && searchResults === null && (
          <AdGrid
            ads={filteredAds}
            title=""
            emptyMessage="No products available"
            loading={loading}
            error={error}
          />
        )}
      </Container>
      <Footer />
    </div>
  );
}

export default Home;

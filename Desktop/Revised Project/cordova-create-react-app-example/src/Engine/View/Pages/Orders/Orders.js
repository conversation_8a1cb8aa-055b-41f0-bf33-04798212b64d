import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardMedia,
  CardContent,
  Divider,
  TextField,
  InputAdornment,
  IconButton,
  Tooltip,
  Snackbar,
  Link,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import ReceiptIcon from "@mui/icons-material/Receipt";
import VisibilityIcon from "@mui/icons-material/Visibility";
import ShoppingBagIcon from "@mui/icons-material/ShoppingBag";
import SearchIcon from "@mui/icons-material/Search";
import DeleteIcon from "@mui/icons-material/Delete";
import DeleteSweepIcon from "@mui/icons-material/DeleteSweep";
import ClearIcon from "@mui/icons-material/Clear";
import { auth, database } from "../../../Config/firebase";

// Import the OrdersController
import {
  getUserOrders,
  getOrderById,
  deleteOrder,
  deleteAllOrders,
} from "../../../Controller/Orders/OrdersController";

// Import the CartController
import { addToCart } from '../../../Controller/Cart/CartController';

// Helper function to get status color
const getStatusColor = (status) => {
  switch (status) {
    case "paid":
    case "Delivered":
      return "success";
    case "Shipped":
      return "info";
    case "Processing":
      return "warning";
    case "Cancelled":
      return "error";
    default:
      return "default";
  }
};

// Helper function to format status label
const formatStatus = (status) => {
  switch (status) {
    case "paid":
      return "Paid";
    default:
      return status || "Processing";
  }
};

// Helper function to format date
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
};

// Helper function to get status display
const getStatusDisplay = (order) => {
  if (order.orderStatus === 'delivered') {
    return {
      mainStatus: `Delivered${order.deliveredAt ? ` on ${formatDate(order.deliveredAt)}` : ''}`,
      color: 'success.dark'
    };
  } else if (order.orderStatus === 'dispatched') {
    return {
      mainStatus: `Dispatched${order.dispatchedAt ? ` on ${formatDate(order.dispatchedAt)}` : ''}`,
      color: 'success.main'
    };
  } else if (order.paymentStatus === 'completed') {
    return {
      mainStatus: `Paid${order.paidAt ? ` on ${formatDate(order.paidAt)}` : ''}`,
      subStatus: 'Awaiting dispatch',
      color: 'warning.main'
    };
  } else {
    return {
      mainStatus: 'Payment Pending',
      color: 'error.main'
    };
  }
};

function Orders() {
  const navigate = useNavigate();
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [orderDetailsOpen, setOrderDetailsOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [deleteAllConfirmOpen, setDeleteAllConfirmOpen] = useState(false);
  const [orderToDelete, setOrderToDelete] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });
  const [dispatchDialogOpen, setDispatchDialogOpen] = useState(false);
  const [dispatchOrderId, setDispatchOrderId] = useState(null);
  const [trackingNumber, setTrackingNumber] = useState("");
  const [actionLoading, setActionLoading] = useState(false);

  // Fetch orders on component mount
  useEffect(() => {
    fetchOrders();
  }, []);

  // Add real-time listener for order status changes
  useEffect(() => {
    if (!auth.currentUser) return;

    const userId = auth.currentUser.uid;
    console.log('Setting up real-time listener for orders...');

    // Listen to the user's orders
    const ordersRef = database.ref(`users/${userId}/orders`);
    const unsubscribe = ordersRef.on('value', async (snapshot) => {
      try {
        console.log('Received real-time order update');
        const result = await getUserOrders();
        if (result.success) {
          setOrders(result.orders);
          setFilteredOrders(result.orders);
          console.log('Orders updated in real-time:', result.orders);
        }
      } catch (err) {
        console.error('Error in real-time order update:', err);
      }
    });

    // Cleanup listener on component unmount
    return () => {
      console.log('Cleaning up orders listener');
      ordersRef.off('value', unsubscribe);
    };
  }, []);  // Empty dependency array since we want this to run once on mount

  // Filter orders when search term changes
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredOrders(orders);
    } else {
      const lowercasedSearch = searchTerm.toLowerCase();
      const filtered = orders.filter(
        (order) =>
          // Search by friendly order ID
          order.friendlyOrderId?.toLowerCase().includes(lowercasedSearch) ||
          // Search by original order ID
          order.id?.toLowerCase().includes(lowercasedSearch) ||
          // Search by item title
          order.title?.toLowerCase().includes(lowercasedSearch) ||
          // Search by brand name
          order.brandName?.toLowerCase().includes(lowercasedSearch) ||
          // Search by seller email
          order.sellerEmail?.toLowerCase().includes(lowercasedSearch) ||
          // Search by status
          formatStatus(order.orderStatus)
            ?.toLowerCase()
            .includes(lowercasedSearch)
      );
      // Maintain the same sorting order as the main orders list
      filtered.sort((a, b) => {
        const dateA = a.createdAt || a.orderDate || 0;
        const dateB = b.createdAt || b.orderDate || 0;
        return dateB - dateA;
      });
      setFilteredOrders(filtered);
    }
  }, [searchTerm, orders]);

  // Fetch orders from Firebase
  const fetchOrders = async () => {
    try {
      console.log('Fetching user orders...');
      setLoading(true);
      const result = await getUserOrders();
      console.log('Fetched orders result:', result);

      if (result.success) {
        // Sort orders by date (newest first)
        const sortedOrders = result.orders.sort((a, b) => {
          const dateA = a.createdAt || a.orderDate || 0;
          const dateB = b.createdAt || b.orderDate || 0;
          return dateB - dateA;
        });
        setOrders(sortedOrders);
        setFilteredOrders(sortedOrders);
        console.log('Orders set in state:', sortedOrders);
      } else {
        setError(result.message || "Failed to fetch orders");
        console.log('Fetch orders error:', result.message);
      }
    } catch (err) {
      console.error("Error fetching orders:", err);
      setError("An error occurred while fetching your orders");
    } finally {
      setLoading(false);
    }
  };

  // Handle view order details
  const handleViewOrder = async (orderId) => {
    try {
      const result = await getOrderById(orderId);

      if (result.success) {
        setSelectedOrder(result.order);
        setOrderDetailsOpen(true);
      } else {
        setError(result.message || "Failed to fetch order details");
      }
    } catch (err) {
      console.error("Error fetching order details:", err);
      setError("An error occurred while fetching order details");
    }
  };

  // Handle close order details dialog
  const handleCloseOrderDetails = () => {
    setOrderDetailsOpen(false);
  };

  // Handle navigate to shop
  const handleNavigateToShop = () => {
    navigate("/");
  };

  // Handle search term change
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Clear search term
  const handleClearSearch = () => {
    setSearchTerm("");
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (order) => {
    setOrderToDelete(order);
    setDeleteConfirmOpen(true);
  };

  // Close delete confirmation dialog
  const handleDeleteCancel = () => {
    setOrderToDelete(null);
    setDeleteConfirmOpen(false);
  };

  // Confirm delete order
  const handleDeleteConfirm = async () => {
    if (!orderToDelete) return;

    try {
      const result = await deleteOrder(orderToDelete.id);

      if (result.success) {
        // Remove the deleted order from the state
        const updatedOrders = orders.filter(
          (order) => order.id !== orderToDelete.id
        );
        setOrders(updatedOrders);

        // Show success message
        setSnackbar({
          open: true,
          message: `Order ${orderToDelete.friendlyOrderId} deleted successfully`,
          severity: "success",
        });
      } else {
        // Show error message
        setSnackbar({
          open: true,
          message: result.message || "Failed to delete order",
          severity: "error",
        });
      }
    } catch (err) {
      console.error("Error deleting order:", err);
      setSnackbar({
        open: true,
        message: "An error occurred while deleting the order",
        severity: "error",
      });
    } finally {
      setOrderToDelete(null);
      setDeleteConfirmOpen(false);
    }
  };

  // Open delete all confirmation dialog
  const handleDeleteAllClick = () => {
    setDeleteAllConfirmOpen(true);
  };

  // Close delete all confirmation dialog
  const handleDeleteAllCancel = () => {
    setDeleteAllConfirmOpen(false);
  };

  // Confirm delete all orders
  const handleDeleteAllConfirm = async () => {
    try {
      const result = await deleteAllOrders();

      if (result.success) {
        // Clear the orders state
        setOrders([]);

        // Show success message
        setSnackbar({
          open: true,
          message: "All orders deleted successfully",
          severity: "success",
        });
      } else {
        // Show error message
        setSnackbar({
          open: true,
          message: result.message || "Failed to delete all orders",
          severity: "error",
        });
      }
    } catch (err) {
      console.error("Error deleting all orders:", err);
      setSnackbar({
        open: true,
        message: "An error occurred while deleting all orders",
        severity: "error",
      });
    } finally {
      setDeleteAllConfirmOpen(false);
    }
  };

  // Close snackbar
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Dispatch order (seller)
  const dispatchOrder = async (orderId, trackingNumber) => {
    setActionLoading(true);
    try {
      // Update in seller's orders
      await database
        .ref(`users/${selectedOrder.sellerId}/orders/${orderId}`)
        .update({
          orderStatus: "dispatched",
          trackingNumber: trackingNumber || "",
          dispatchedAt: Date.now(),
        });
      // Optionally, update in buyer's orders as well
      await database
        .ref(`users/${selectedOrder.buyerId}/orders/${orderId}`)
        .update({
          orderStatus: "dispatched",
          trackingNumber: trackingNumber || "",
          dispatchedAt: Date.now(),
        });
      setSnackbar({
        open: true,
        message: "Order marked as dispatched!",
        severity: "success",
      });
      fetchOrders();
    } catch (err) {
      setSnackbar({
        open: true,
        message: "Failed to dispatch order.",
        severity: "error",
      });
    } finally {
      setActionLoading(false);
    }
  };

  // Confirm delivery (buyer)
  const confirmDelivery = async (orderId) => {
    setActionLoading(true);
    try {
      // Get the current user's ID token
      const idToken = await auth.currentUser.getIdToken(true);
      
      // Make API call to backend instead of direct database update
      const response = await fetch(`http://localhost:5000/api/orders/${orderId}/confirm-delivery`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`
        },
        body: JSON.stringify({
          userId: auth.currentUser.uid,
          orderStatus: 'delivered',
          confirmedAt: new Date().toISOString()
        })
      });

      if (!response.ok) {
        throw new Error('Failed to confirm delivery');
      }

      const data = await response.json();
      if (data.success) {
        setSnackbar({
          open: true,
          message: "Order marked as delivered!",
          severity: "success",
        });
        // The real-time listener will update the UI
      } else {
        throw new Error(data.message || 'Failed to confirm delivery');
      }
    } catch (err) {
      console.error('Error confirming delivery:', err);
      setSnackbar({
        open: true,
        message: "Failed to confirm delivery: " + err.message,
        severity: "error",
      });
    } finally {
      setActionLoading(false);
    }
  };

  // Add this function in Orders component
  const handleBuyAgain = (order) => {
    // Prepare the item object for addToCart
    const cartItem = {
      adId: order.adId || order.itemId, // support both field names
      title: order.title || order.itemName || order.brandName,
      price: order.price,
      brandName: order.brandName,
      photos: order.photos,
      sellerId: order.sellerId,
      // Add any other fields your cart expects
    };
    const result = addToCart(cartItem);
    if (result.success) {
      setSnackbar({
        open: true,
        message: 'Item added to cart!',
        severity: 'success',
      });
    } else {
      setSnackbar({
        open: true,
        message: result.message || 'Failed to add item to cart',
        severity: 'error',
      });
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
        <ReceiptIcon sx={{ fontSize: 30, mr: 2, color: "primary.main" }} />
        <Typography variant="h5" component="h1">
          Your Orders
        </Typography>
      </Box>

      {loading ? (
        <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      ) : orders.length > 0 ? (
        <>
          {/* Search and Actions Bar */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              mb: 2,
              flexWrap: "wrap",
              gap: 2,
            }}
          >
            {/* Search Field */}
            <TextField
              placeholder="Search orders..."
              variant="outlined"
              size="small"
              value={searchTerm}
              onChange={handleSearchChange}
              sx={{ minWidth: 300, flexGrow: 1 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: searchTerm && (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="clear search"
                      onClick={handleClearSearch}
                      edge="end"
                      size="small"
                    >
                      <ClearIcon fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            {/* Delete All Button */}
            <Tooltip title="Delete All Orders">
              <Button
                variant="outlined"
                color="error"
                startIcon={<DeleteSweepIcon />}
                onClick={handleDeleteAllClick}
                disabled={orders.length === 0}
              >
                Delete All
              </Button>
            </Tooltip>
          </Box>

          {/* Orders Table */}
          <TableContainer component={Paper} sx={{ mt: 2 }}>
            <Table aria-label="orders table">
              <TableHead>
                <TableRow sx={{ backgroundColor: "primary.light" }}>
                  <TableCell>Order ID</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Item</TableCell>
                  <TableCell>Total</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredOrders.length > 0 ? (
                  filteredOrders.map((order) => (
                    <TableRow key={order.id} hover>
                      <TableCell component="th" scope="row">
                        {order.friendlyOrderId ||
                          order.id.substring(0, 8) + "..."}
                      </TableCell>
                      <TableCell>{order.formattedDate}</TableCell>
                      <TableCell>
                        {/* {order.title} */}
                        {order.brandName && (
                          <Typography
                            variant="caption"
                            display="block"
                            color="text.secondary"
                          >
                            Brand: {order.brandName}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>{order.formattedTotal}</TableCell>
                      <TableCell>
                        <Chip
                          label={formatStatus(order.orderStatus)}
                          color={getStatusColor(order.orderStatus)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="right">
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "flex-end",
                            gap: 1,
                          }}
                        >
                          <Tooltip title="View Order Details">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleViewOrder(order.id)}
                            >
                              <VisibilityIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete Order">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleDeleteClick(order)}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          {order.buyerId === auth.currentUser?.uid && (
                            <Tooltip title="Buy Again">
                              <span>
                                <IconButton
                                  size="small"
                                  color="primary"
                                  onClick={() => handleBuyAgain(order)}
                                >
                                  <ShoppingBagIcon fontSize="small" />
                                </IconButton>
                              </span>
                            </Tooltip>
                          )}
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <Typography variant="body1" sx={{ py: 2 }}>
                        No orders match your search.
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </>
      ) : (
        <Paper sx={{ p: 3, textAlign: "center" }}>
          <Typography variant="body1">
            You don't have any orders yet.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            sx={{ mt: 2 }}
            startIcon={<ShoppingBagIcon />}
            onClick={handleNavigateToShop}
          >
            Start Shopping
          </Button>
        </Paper>
      )}

      {/* Order Details Dialog */}
      <Dialog
        open={orderDetailsOpen}
        onClose={handleCloseOrderDetails}
        maxWidth="md"
        fullWidth
      >
        {selectedOrder && (
          <>
            <DialogTitle>Order Details</DialogTitle>
            <DialogContent dividers>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom>
                    Order Information
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Order ID:{" "}
                      {selectedOrder.friendlyOrderId || selectedOrder.id}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Date: {selectedOrder.formattedDate}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Status: {getStatusDisplay(selectedOrder).mainStatus}
                    </Typography>
                    {selectedOrder.orderStatus === 'delivered' && selectedOrder.deliveredAt && (
                      <Typography variant="body2" color="success.dark">
                        Delivered on: {formatDate(selectedOrder.deliveredAt)}
                      </Typography>
                    )}
                    <Typography variant="body2" color="text.secondary">
                      Total: {selectedOrder.formattedTotal}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom>
                    Item Details
                  </Typography>
                  <Card sx={{ display: "flex", mb: 2 }}>
                    {selectedOrder.photos &&
                      selectedOrder.photos.length > 0 && (
                        <CardMedia
                          component="img"
                          sx={{ width: 100, height: 100, objectFit: "contain" }}
                          image={
                            // Try to find the cover photo first
                            (Array.isArray(selectedOrder.photos) &&
                              selectedOrder.photos.find(
                                (photo) => photo.isCover
                              )?.url) ||
                            // Then try the first photo's URL
                            (Array.isArray(selectedOrder.photos) &&
                              selectedOrder.photos[0]?.url) ||
                            // Then try if photos[0] is a string URL directly
                            (Array.isArray(selectedOrder.photos)
                              ? selectedOrder.photos[0]
                              : "")
                          }
                          alt={selectedOrder.title}
                        />
                      )}
                    <CardContent sx={{ flex: "1 0 auto" }}>
                      <Typography component="div" variant="h6">
                        {/* {selectedOrder.title} */}
                      </Typography>
                      {selectedOrder.brandName && (
                        <Typography variant="body2" color="text.secondary">
                          Brand: {selectedOrder.brandName}
                        </Typography>
                      )}
                      <Typography variant="body2" color="text.secondary">
                        Quantity: {selectedOrder.quantity || 1}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Price: {selectedOrder.formattedTotal}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ cursor: selectedOrder.sellerId ? 'pointer' : 'default', textDecoration: selectedOrder.sellerId ? 'underline' : 'none', display: 'inline-block' }}
                    onClick={() => {
                      if (selectedOrder.sellerId) {
                        navigate(`/seller/${selectedOrder.sellerId}`);
                      }
                    }}
                  >
                    Seller: {selectedOrder.sellerName || selectedOrder.sellerDisplayName || selectedOrder.sellerInfo?.displayName || selectedOrder.sellerEmail || "Unknown"}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  {/* Seller-side: Dispatch Item button if order is paid and user is seller */}
                  {selectedOrder &&
                    selectedOrder.orderStatus === "paid" &&
                    selectedOrder.sellerId === auth.currentUser?.uid && (
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={() => {
                          setDispatchOrderId(selectedOrder.id);
                          setDispatchDialogOpen(true);
                        }}
                        disabled={actionLoading}
                      >
                        Dispatch Item
                      </Button>
                    )}
                  {/* Buyer-side: Dispatched badge, tracking link, and Confirm Delivery button */}
                  {selectedOrder &&
                    selectedOrder.orderStatus === "dispatched" && (
                      <Box sx={{ mt: 2 }}>
                        <Chip label="Dispatched" color="info" sx={{ mr: 2 }} />
                        {selectedOrder.trackingNumber && (
                          <Link
                            component="a"
                            href={`https://t.17track.net/en#nums=${selectedOrder.trackingNumber}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="tracking-link"
                          >
                            Track Package
                          </Link>
                        )}
                        {selectedOrder.buyerId === auth.currentUser?.uid && (
                          <Button
                            variant="contained"
                            color="success"
                            onClick={() => confirmDelivery(selectedOrder.id)}
                            disabled={actionLoading}
                          >
                            Confirm Delivery
                          </Button>
                        )}
                      </Box>
                    )}
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseOrderDetails}>Close</Button>
              {selectedOrder && selectedOrder.buyerId === auth.currentUser?.uid && (
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<ShoppingBagIcon />}
                  onClick={() => handleBuyAgain(selectedOrder)}
                >
                  Buy Again
                </Button>
              )}
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteConfirmOpen}
        onClose={handleDeleteCancel}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete order{" "}
            {orderToDelete?.friendlyOrderId || "this order"}? This action cannot
            be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete All Confirmation Dialog */}
      <Dialog
        open={deleteAllConfirmOpen}
        onClose={handleDeleteAllCancel}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Confirm Delete All</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete all orders? This action cannot be
            undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteAllCancel}>Cancel</Button>
          <Button
            onClick={handleDeleteAllConfirm}
            color="error"
            variant="contained"
          >
            Delete All
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dispatch Dialog for Seller */}
      <Dialog open={dispatchDialogOpen} onClose={() => setDispatchDialogOpen(false)}>
        <DialogTitle>Dispatch Item</DialogTitle>
        <DialogContent>
          <TextField
            label="Tracking Number (optional)"
            value={trackingNumber}
            onChange={e => setTrackingNumber(e.target.value)}
            fullWidth
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDispatchDialogOpen(false)} disabled={actionLoading}>Cancel</Button>
          <Button
            onClick={async () => {
              await dispatchOrder(dispatchOrderId, trackingNumber);
              setDispatchDialogOpen(false);
              setTrackingNumber("");
            }}
            variant="contained"
            disabled={actionLoading}
          >
            Confirm Dispatch
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        message={snackbar.message}
      />
    </Box>
  );
}

export default Orders;

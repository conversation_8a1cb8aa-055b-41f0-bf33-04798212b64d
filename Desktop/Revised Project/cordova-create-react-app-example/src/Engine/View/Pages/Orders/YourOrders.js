import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Button,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider
} from '@mui/material';
import { auth, database } from '../../../Config/firebase';
import { formatPriceWithSymbol, formatDate } from '../../../Utils/CurrencyUtils';
import { useNavigate } from 'react-router-dom';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';

function YourOrders() {
  const navigate = useNavigate();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [confirmDeliveryDialog, setConfirmDeliveryDialog] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/orders/buyer', {
        headers: {
          'Authorization': `Bearer ${await auth.currentUser.getIdToken()}`
        }
      });
      const data = await response.json();
      if (data.success) {
        // Sort orders by date (newest first)
        const sortedOrders = data.orders.sort((a, b) => {
          const dateA = a.createdAt || a.orderDate || 0;
          const dateB = b.createdAt || b.orderDate || 0;
          return dateB - dateA;
        });
        setOrders(sortedOrders);
      } else {
        throw new Error(data.message);
      }
    } catch (err) {
      setError('Failed to fetch orders: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const confirmDelivery = async (orderId) => {
    setActionLoading(true);
    try {
      // First update local order status
      const updatedOrders = orders.map(order => {
        if (order.id === orderId) {
          return { ...order, orderStatus: 'delivered' };
        }
        return order;
      });
      setOrders(updatedOrders);

      const response = await fetch(`http://localhost:5000/api/orders/${orderId}/confirm-delivery`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await auth.currentUser.getIdToken()}`
        },
        body: JSON.stringify({
          userId: auth.currentUser.uid,
          orderStatus: 'delivered',
          confirmedAt: new Date().toISOString()
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update delivery status');
      }

      const data = await response.json();
      if (data.success) {
        setConfirmDeliveryDialog(false);
        setSelectedOrder(null);
        // Refresh orders to get latest state
        fetchOrders();
      } else {
        throw new Error(data.message || 'Failed to confirm delivery');
      }
    } catch (err) {
      console.error('Delivery confirmation error:', err);
      setError('Failed to confirm delivery: ' + err.message);
      // Revert local state on error
      fetchOrders();
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusDisplay = (order) => {
    if (order.orderStatus === 'delivered') {
      return { 
        mainStatus: `Delivered${order.deliveredAt ? ` on ${formatDate(order.deliveredAt)}` : ''}`,
        color: 'success.dark'
      };
    } else if (order.orderStatus === 'dispatched') {
      return { 
        mainStatus: `Dispatched${order.dispatchedAt ? ` on ${formatDate(order.dispatchedAt)}` : ''}`,
        color: 'success.main'
      };
    } else if (order.paymentStatus === 'completed') {
      return { 
        mainStatus: `Paid${order.paidAt ? ` on ${formatDate(order.paidAt)}` : ''}`,
        subStatus: 'Awaiting dispatch',
        color: 'warning.main'
      };
    } else {
      return { 
        mainStatus: 'Payment Pending',
        color: 'error.main'
      };
    }
  };

  // Add Buy Again functionality
  const handleBuyAgain = async (order) => {
    try {
      setActionLoading(true);
      
      // Get the current user's ID token
      const idToken = await auth.currentUser.getIdToken(true);
      
      // Make API call to create a new order based on the previous one
      const response = await fetch('http://localhost:5000/api/orders/buy-again', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`
        },
        body: JSON.stringify({
          originalOrderId: order.id,
          userId: auth.currentUser.uid,
          itemId: order.itemId,
          quantity: order.quantity || 1,
          price: order.price,
          sellerId: order.sellerId
        })
      });

      const data = await response.json();
      
      if (data.success) {
        // Navigate to the cart page
        navigate('/cart');
      } else {
        throw new Error(data.message || 'Failed to add item to cart');
      }
    } catch (err) {
      setError('Failed to add item to cart: ' + err.message);
    } finally {
      setActionLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom fontWeight="bold">
        Your Orders
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {orders.length === 0 ? (
        <Typography variant="body1" sx={{ textAlign: 'center', mt: 4 }}>
          You haven't placed any orders yet.
        </Typography>
      ) : (
        orders.map((order) => {
          const status = getStatusDisplay(order);
          return (
            <Card key={order.id} sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="h6">
                  {order.itemName || order.brandName}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Order ID: {order.id}
                </Typography>
                <Box sx={{ mt: 2, mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body1" sx={{ color: status.color, fontWeight: 'medium' }}>
                      Status: {status.mainStatus}
                    </Typography>
                    {status.subStatus && (
                      <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                        ({status.subStatus})
                      </Typography>
                    )}
                  </Box>
                </Box>

                {/* Order Timeline */}
                <Box sx={{ mt: 3, mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Order Timeline:
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, ml: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ 
                        width: 8, 
                        height: 8, 
                        borderRadius: '50%', 
                        bgcolor: order.paymentStatus === 'completed' ? 'success.main' : 'grey.400' 
                      }} />
                      <Typography variant="body2" color={order.paymentStatus === 'completed' ? 'text.primary' : 'text.secondary'}>
                        Payment
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ 
                        width: 8, 
                        height: 8, 
                        borderRadius: '50%', 
                        bgcolor: order.orderStatus === 'dispatched' || order.orderStatus === 'delivered' ? 'success.main' : 'grey.400' 
                      }} />
                      <Typography variant="body2" color={order.orderStatus === 'dispatched' || order.orderStatus === 'delivered' ? 'text.primary' : 'text.secondary'}>
                        Dispatch
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ 
                        width: 8, 
                        height: 8, 
                        borderRadius: '50%', 
                        bgcolor: order.orderStatus === 'delivered' ? 'success.main' : 'grey.400' 
                      }} />
                      <Typography variant="body2" color={order.orderStatus === 'delivered' ? 'text.primary' : 'text.secondary'}>
                        Delivery
                      </Typography>
                    </Box>
                  </Box>
                </Box>

                <Typography variant="body2">
                  Price: {formatPriceWithSymbol(order.price, 'EUR')}
                </Typography>
                {order.trackingNumber && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2">
                      Tracking Number: {order.trackingNumber}
                    </Typography>
                    <Button
                      variant="text"
                      size="small"
                      color="primary"
                      onClick={() => {
                        window.open(`https://t.17track.net/en#nums=${order.trackingNumber}`, '_blank');
                      }}
                      sx={{ mt: 0.5 }}
                    >
                      Track Package
                    </Button>
                  </Box>
                )}
                <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                  {order.orderStatus === 'dispatched' && (
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => {
                        setSelectedOrder(order);
                        setConfirmDeliveryDialog(true);
                      }}
                    >
                      Confirm Delivery
                    </Button>
                  )}
                  {/* Add Buy Again button */}
                  <Button
                    variant="outlined"
                    color="primary"
                    startIcon={<ShoppingCartIcon />}
                    onClick={() => handleBuyAgain(order)}
                    disabled={actionLoading}
                  >
                    Buy Again
                  </Button>
                </Box>
              </CardContent>
            </Card>
          );
        })
      )}

      {/* Confirm Delivery Dialog */}
      <Dialog 
        open={confirmDeliveryDialog} 
        onClose={() => setConfirmDeliveryDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Confirm Item Received</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            If you have received your item, please confirm below.
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            By clicking confirm, you acknowledge that:
          </Typography>
          <Box component="ul" sx={{ mt: 1, mb: 2 }}>
            <Typography component="li" variant="body2" color="text.secondary">
              You have received the item in good condition
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              This action cannot be undone
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => {
              setConfirmDeliveryDialog(false);
              setSelectedOrder(null);
            }}
            disabled={actionLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={() => confirmDelivery(selectedOrder.id)}
            variant="contained"
            color="primary"
            disabled={actionLoading}
            startIcon={actionLoading ? <CircularProgress size={20} /> : null}
          >
            Confirm Receipt
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default YourOrders; 
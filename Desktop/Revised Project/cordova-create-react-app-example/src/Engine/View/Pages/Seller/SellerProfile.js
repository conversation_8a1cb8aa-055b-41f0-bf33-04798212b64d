import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  <PERSON><PERSON>r,
  <PERSON><PERSON>,
} from "@mui/material";
import HomeIcon from "@mui/icons-material/Home";
import { database } from "../../../Config/firebase";

const SellerProfile = () => {
  const { sellerId } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [sellerInfo, setSellerInfo] = useState(null);
  const [activeListings, setActiveListings] = useState([]);
  const [tabValue, setTabValue] = useState(0);

  useEffect(() => {
    fetchSellerData();
  }, [sellerId]);

  const fetchSellerData = async () => {
    setLoading(true);
    setError("");

    try {
      // Fetch seller information
      const sellerSnapshot = await database.ref(`users/${sellerId}`).once("value");
      const sellerData = sellerSnapshot.val();

      if (!sellerData) {
        setError("Seller not found");
        setLoading(false);
        return;
      }

      setSellerInfo({
        displayName: sellerData.displayName || "Anonymous Seller",
        profilePicture: sellerData.profilePicture || null,
        rating: sellerData.rating || 0,
        totalSales: sellerData.totalSales || 0,
        memberSince: sellerData.joinedSince || sellerData.createdAt || null,
        location: sellerData.location || null
      });

      // Fetch seller's active listings
      const listingsSnapshot = await database
        .ref("publishedAds")
        .orderByChild("userId")
        .equalTo(sellerId)
        .once("value");

      const listings = [];
      listingsSnapshot.forEach((snapshot) => {
        const listing = snapshot.val();
        if (listing.status === "published") {
          listings.push({
            ...listing,
            id: snapshot.key
          });
        }
      });

      setActiveListings(listings);
    } catch (err) {
      console.error("Error fetching seller data:", err);
      setError("Failed to load seller information");
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return "Unknown date";
    return new Date(timestamp).toLocaleDateString();
  };

  const handleBackToHome = () => {
    navigate("/");
  };

  if (loading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button
          variant="outlined"
          startIcon={<HomeIcon />}
          onClick={handleBackToHome}
        >
          Back to Home
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 2, maxWidth: 1200, margin: "0 auto" }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Button
          variant="outlined"
          startIcon={<HomeIcon />}
          onClick={handleBackToHome}
          sx={{ mb: 2 }}
        >
          Back to Home
        </Button>
       
      </Box>

      {/* Seller Information */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center" }}>
                {sellerInfo.profilePicture ? (
                  <img
                    src={sellerInfo.profilePicture}
                    alt={sellerInfo.displayName}
                    style={{
                      width: "150px",
                      height: "150px",
                      borderRadius: "50%",
                      objectFit: "cover",
                      marginBottom: "16px"
                    }}
                  />
                ) : (
                  <Box
                    sx={{
                      width: "150px",
                      height: "150px",
                      borderRadius: "50%",
                      bgcolor: "grey.300",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      marginBottom: "16px"
                    }}
                  >
                    <Typography variant="h3" color="text.secondary">
                      {sellerInfo.displayName?.charAt(0) || "?"}
                    </Typography>
                  </Box>
                )}
                <Typography variant="h5" gutterBottom>
                  {sellerInfo.displayName}
                </Typography>
              
              </Box>
            </Grid>
            <Grid item xs={12} md={8}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Seller Information
                </Typography>
                <Typography variant="body1">
                  <strong>Location:</strong>{" "}
                  {sellerInfo.location && typeof sellerInfo.location === "object"
                    ? sellerInfo.location.formattedLocation
                      || [
                        sellerInfo.location.address,
                        sellerInfo.location.state && sellerInfo.location.state.name,
                        sellerInfo.location.country && sellerInfo.location.country.name
                      ].filter(Boolean).join(", ")
                      || "Not provided"
                    : sellerInfo.location || "Not provided"}
                </Typography>
                
                <Typography variant="body1">
                  <strong>Member since:</strong> {formatDate(sellerInfo.memberSince)}
                </Typography>
              </Box>
              <Divider sx={{ my: 2 }} />
              <Box>
               
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body1">
                      <strong>Rating:</strong> {sellerInfo.rating.toFixed(1)} ⭐
                    </Typography>
                  </Grid>
                 
                </Grid>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Listings */}
      <Box sx={{ mb: 2 }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label={`Shop (${activeListings.length})`} />
          <Tab label="Reviews" />
        </Tabs>
      </Box>

      {tabValue === 0 && (
        <Grid container spacing={2}>
          {activeListings.length === 0 ? (
            <Grid item xs={12}>
              <Typography variant="body1" sx={{ textAlign: "center", py: 4 }}>
                No active listings found
              </Typography>
            </Grid>
          ) : (
            activeListings.map((listing) => (
              <Grid item xs={12} sm={6} md={4} key={listing.id}>
                <Card
                  sx={{
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                    cursor: "pointer"
                  }}
                  onClick={() => navigate(`/ad/${listing.id}`)}
                >
                  {listing.photos && listing.photos.length > 0 && (
                    <Box
                      sx={{
                        height: 200,
                        overflow: "hidden",
                        position: "relative"
                      }}
                    >
                      <img
                        src={listing.photos[0].url}
                        alt={listing.itemName || listing.brandName}
                        style={{
                          width: "100%",
                          height: "100%",
                          objectFit: "cover"
                        }}
                      />
                    </Box>
                  )}
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Typography variant="h6" gutterBottom>
                      {listing.itemName || listing.brandName}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {listing.description?.substring(0, 100)}...
                    </Typography>
                    <Typography variant="h6" color="primary" sx={{ mt: 2 }}>
                      {listing.price === 0
                        ? "Free"
                        : `$${listing.price.toFixed(2)}`}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))
          )}
        </Grid>
      )}

      {tabValue === 1 && (
        <Box sx={{ p: 2 }}>
          <Typography variant="body1" sx={{ textAlign: "center", py: 4 }}>
            Reviews coming soon
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default SellerProfile; 
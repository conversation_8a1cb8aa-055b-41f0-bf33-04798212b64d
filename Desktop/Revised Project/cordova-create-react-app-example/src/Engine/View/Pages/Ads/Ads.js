import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Divider,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Grid,
  TextField,
  Snackbar,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import PublishIcon from "@mui/icons-material/Publish";
import UnpublishedIcon from "@mui/icons-material/Unpublished";
import InfoIcon from "@mui/icons-material/Info";
import { useNavigate } from "react-router-dom";
import {
  formatPriceWithSymbol,
  calculateFinalPrice,
} from "../../../Utils/CurrencyUtils";
import { auth, database } from "../../../Config/firebase";

// Import controller functions
import {
  getUserAds,
  deleteAd,
  changeAdStatus,
  deleteAdPhotos,
  deleteSinglePhoto,
  getSoldItemDetails,
  deleteSoldItem,
  republishSoldItem,
} from "../../../Controller/Ads/AdsController";

function Ads() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [ads, setAds] = useState({
    drafts: [],
    published: [],
    sold: [],
    draftCount: 0,
    publishedCount: 0,
    soldCount: 0
  });
  const [tabValue, setTabValue] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedAd, setSelectedAd] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);
  const [actionSuccess, setActionSuccess] = useState("");
  const [buyerDetailsDialogOpen, setBuyerDetailsDialogOpen] = useState(false);
  const [selectedSoldItem, setSelectedSoldItem] = useState(null);
  const [buyerDetailsLoading, setBuyerDetailsLoading] = useState(false);
  const [deleteSoldDialogOpen, setDeleteSoldDialogOpen] = useState(false);
  const [republishDialogOpen, setRepublishDialogOpen] = useState(false);
  const [selectedSoldAd, setSelectedSoldAd] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [soldSearchTerm, setSoldSearchTerm] = useState("");
  const [draftSearchTerm, setDraftSearchTerm] = useState("");
  const [dispatchDialogOpen, setDispatchDialogOpen] = useState(false);
  const [dispatchOrderId, setDispatchOrderId] = useState(null);
  const [trackingNumber, setTrackingNumber] = useState("");
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  const navigate = useNavigate();

  // Add the real-time listener for sold items
  useEffect(() => {
    if (!auth.currentUser) return;

    const userId = auth.currentUser.uid;
    console.log('Setting up real-time listener for sold items...');

    // Listen to the user's soldItems
    const soldItemsRef = database.ref(`users/${userId}/soldItems`);
    const unsubscribe = soldItemsRef.on('value', async (snapshot) => {
      try {
        console.log('Received real-time soldItems update');
        const soldItemsData = snapshot.val() || {};
        
        // Convert to array and format
        const soldItems = Object.entries(soldItemsData).map(([id, item]) => ({
          ...item,
          id,
          formattedDate: new Date(item.createdAt || Date.now()).toLocaleDateString()
        }));

        console.log('Processed sold items:', soldItems);

        // Update the ads state with new sold items
        setAds(prevAds => ({
          ...prevAds,
          sold: soldItems,
          soldCount: soldItems.length
        }));

      } catch (err) {
        console.error('Error in real-time soldItems update:', err);
      }
    });

    // Cleanup listener on component unmount
    return () => {
      console.log('Cleaning up soldItems listener');
      soldItemsRef.off('value', unsubscribe);
    };
  }, []);  // Empty dependency array since we want this to run once on mount

  // Fetch ads on component mount
  useEffect(() => {
    fetchAds();
  }, []);

  // Function to fetch ads
  const fetchAds = async () => {
    setLoading(true);
    setError("");
    try {
      const userId = auth.currentUser?.uid;
      if (!userId) {
        throw new Error('User not authenticated');
      }

      console.log('Fetching user ads...');
      
      // Get drafts and published ads
      const result = await getUserAds();
      console.log('Fetched ads result:', result);

      // Get sold items directly from Firebase
      const soldItemsSnapshot = await database.ref(`users/${userId}/soldItems`).once('value');
      const soldItemsData = soldItemsSnapshot.val() || {};
      
      // Convert sold items to array and format
      const soldItems = Object.entries(soldItemsData).map(([id, item]) => ({
        ...item,
        id,
        formattedDate: new Date(item.createdAt || Date.now()).toLocaleDateString()
      }));

      console.log('Fetched sold items:', soldItems);

      if (result.success) {
        setAds({
          drafts: result.data.drafts || [],
          published: result.data.published || [],
          sold: soldItems,
          draftCount: result.data.drafts?.length || 0,
          publishedCount: result.data.published?.length || 0,
          soldCount: soldItems.length
        });
      } else {
        setError(result.message);
      }
    } catch (err) {
      console.error('Error in fetchAds:', err);
      setError("Error fetching ads: " + err.message);
    } finally {
      setLoading(false);
    }
  };

  // Add the dispatchOrder function
  const dispatchOrder = async (orderId, trackingNumber) => {
    try {
      setActionLoading(true);
      console.log('Dispatching order:', orderId, 'with tracking:', trackingNumber);
      
      // Get the current user's ID token
      const idToken = await auth.currentUser.getIdToken(true);
      const userId = auth.currentUser.uid;
      console.log('Got auth token, making dispatch request');

      const response = await fetch(`http://localhost:5000/api/orders/${orderId}/dispatch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`
        },
        body: JSON.stringify({
          trackingNumber: trackingNumber || '',
          orderStatus: 'dispatched',
          userId: userId,
          dispatchedAt: Date.now() // Only add dispatch timestamp
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to dispatch order: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      if (data.success) {
        setSnackbar({
          open: true,
          message: 'Order dispatched successfully!',
          severity: 'success'
        });
        // Close the dispatch dialog
        setDispatchDialogOpen(false);
        setTrackingNumber('');
        // Refresh the ads list
        await fetchAds();
      } else {
        throw new Error(data.message || 'Failed to dispatch order');
      }
    } catch (error) {
      console.error('Dispatch error:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Failed to dispatch order',
        severity: 'error'
      });
    } finally {
      setActionLoading(false);
    }
  };

  // Update the checkOrderStatus function to include auth token
  useEffect(() => {
    const checkOrderStatus = async () => {
      if (!auth.currentUser) return;

      try {
        const idToken = await auth.currentUser.getIdToken(true);
        const userId = auth.currentUser.uid;

        // Set up real-time listener for sold items
        const soldItemsRef = database.ref(`users/${userId}/soldItems`);
        soldItemsRef.on('value', async (snapshot) => {
          const soldItems = snapshot.val();
          if (soldItems) {
            const updatedSoldItems = Object.entries(soldItems).map(([id, item]) => ({
              ...item,
              id
            }));
            
            // Update the ads state with new sold items
            setAds(prevAds => ({
              ...prevAds,
              sold: updatedSoldItems
            }));
          }
        });

        // Cleanup listener
        return () => {
          soldItemsRef.off('value');
        };
      } catch (error) {
        console.error('Error setting up sold items listener:', error);
      }
    };

    checkOrderStatus();
  }, [auth.currentUser]); // Only re-run if auth.currentUser changes

  // Update the status display logic
  const getOrderStatus = (ad) => {
    console.log('Getting order status for:', ad); // Debug log
    if (ad.orderStatus === 'delivered') {
      return {
        text: 'Delivered',
        color: 'success.dark'
      };
    } else if (ad.orderStatus === 'dispatched') {
      return {
        text: 'Dispatched',
        color: 'success.main'
      };
    } else if (ad.paymentStatus === 'completed') {
      return {
        text: 'Paid (Awaiting dispatch)',
        color: 'warning.main'
      };
    } else {
      return {
        text: 'Payment Pending',
        color: 'error.main'
      };
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (ad) => {
    setSelectedAd(ad);
    setDeleteDialogOpen(true);
  };

  // Close delete confirmation dialog
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setSelectedAd(null);
  };

  // Confirm delete
  const handleDeleteConfirm = async () => {
    if (!selectedAd) return;

    setActionLoading(true);
    try {
      const result = await deleteAd(selectedAd.id);
      if (result.success) {
        setActionSuccess("Ad deleted successfully");
        // Refresh ads list
        fetchAds();
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError("Error deleting ad: " + err.message);
    } finally {
      setActionLoading(false);
      setDeleteDialogOpen(false);
      setSelectedAd(null);

      // Clear success message after 3 seconds
      if (actionSuccess) {
        setTimeout(() => setActionSuccess(""), 3000);
      }
    }
  };

  // Handle edit ad
  const handleEditAd = (ad) => {
    // Make sure we have the adId property set correctly
    const adWithId = {
      ...ad,
      adId: ad.adId || ad.id, // Ensure adId is set (might be stored as id in some cases)
    };

    console.log("Editing ad with data:", adWithId);

    // Navigate to PlaceAd with the ad data
    navigate("/place-ad", { state: { adData: adWithId } });
  };

  // Handle status change (publish/unpublish)
  const handleStatusChange = async (ad) => {
    const newStatus = ad.status === "draft" ? "published" : "draft";

    setActionLoading(true);
    try {
      const result = await changeAdStatus(ad.id, newStatus);
      if (result.success) {
        setActionSuccess(
          `Ad ${
            newStatus === "published" ? "published" : "unpublished"
          } successfully`
        );
        // Refresh ads list
        fetchAds();
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError(
        `Error ${
          newStatus === "published" ? "publishing" : "unpublishing"
        } ad: ` + err.message
      );
    } finally {
      setActionLoading(false);

      // Clear success message after 3 seconds
      if (actionSuccess) {
        setTimeout(() => setActionSuccess(""), 3000);
      }
    }
  };

  // Format date
  const formatDate = (timestamp) => {
    if (!timestamp) return "Unknown date";
    const date = new Date(timestamp);
    return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
  };

  // Handle sold item click
  const handleSoldItemClick = async (ad) => {
    setSelectedSoldItem(ad);
    setBuyerDetailsLoading(true);
    try {
      const result = await getSoldItemDetails(ad.id);
      if (result.success) {
        setSelectedSoldItem(result.data);
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError("Error fetching buyer details: " + err.message);
    } finally {
      setBuyerDetailsLoading(false);
      setBuyerDetailsDialogOpen(true);
    }
  };

  // Close buyer details dialog
  const handleCloseBuyerDetails = () => {
    setBuyerDetailsDialogOpen(false);
    setSelectedSoldItem(null);
  };

  // Handler for delete sold item
  const handleDeleteSoldClick = (ad) => {
    console.log("Delete clicked for sold item:", ad);
    setSelectedSoldAd(ad);
    setDeleteSoldDialogOpen(true);
  };
  const handleDeleteSoldConfirm = async () => {
    console.log("Delete confirm for sold item:", selectedSoldAd);
    if (!selectedSoldAd) return;
    setActionLoading(true);
    try {
      const result = await deleteSoldItem(selectedSoldAd.id);
      console.log("Delete result:", result);
      if (result.success) {
        setActionSuccess("Sold item deleted successfully");
        fetchAds();
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError("Error deleting sold item: " + err.message);
    } finally {
      setActionLoading(false);
      setDeleteSoldDialogOpen(false);
      setSelectedSoldAd(null);
      if (actionSuccess) {
        setTimeout(() => setActionSuccess(""), 3000);
      }
    }
  };

  // Handler for republish sold item
  const handleRepublishClick = (ad) => {
    console.log("Republish clicked for sold item:", ad);
    setSelectedSoldAd(ad);
    setRepublishDialogOpen(true);
  };
  const handleRepublishConfirm = async () => {
    console.log("Republish confirm for sold item:", selectedSoldAd);
    if (!selectedSoldAd) return;
    setActionLoading(true);
    try {
      const result = await republishSoldItem(selectedSoldAd.id);
      console.log("Republish result:", result);
      if (result.success) {
        setActionSuccess("Sold item republished as draft");
        fetchAds();
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError("Error republishing sold item: " + err.message);
    } finally {
      setActionLoading(false);
      setRepublishDialogOpen(false);
      setSelectedSoldAd(null);
      if (actionSuccess) {
        setTimeout(() => setActionSuccess(""), 3000);
      }
    }
  };

  // Filtered ads for Active and Sold
  const filteredPublished = ads.published.filter(ad => {
    const term = searchTerm.toLowerCase();
    return (
      (ad.itemName && ad.itemName.toLowerCase().includes(term)) ||
      (ad.brandName && ad.brandName.toLowerCase().includes(term)) ||
      (ad.description && ad.description.toLowerCase().includes(term))
    );
  });

  const filteredSold = ads.sold.filter(ad => {
    const term = soldSearchTerm.toLowerCase();
    return (
      (ad.itemName && ad.itemName.toLowerCase().includes(term)) ||
      (ad.brandName && ad.brandName.toLowerCase().includes(term)) ||
      (ad.description && ad.description.toLowerCase().includes(term))
    );
  }).sort((a, b) => {
    // First sort by order status (paid -> dispatched -> delivered)
    const statusOrder = {
      'paid': 0,
      'dispatched': 1,
      'delivered': 2
    };
    const statusA = statusOrder[a.orderStatus] || 0;
    const statusB = statusOrder[b.orderStatus] || 0;
    if (statusA !== statusB) {
      return statusA - statusB;
    }

    // Then sort by date (newest first)
    const dateA = a.soldAt || a.createdAt || 0;
    const dateB = b.soldAt || b.createdAt || 0;
    return dateB - dateA;
  });

  const filteredDrafts = ads.drafts.filter(ad => {
    const term = draftSearchTerm.toLowerCase();
    return (
      (ad.itemName && ad.itemName.toLowerCase().includes(term)) ||
      (ad.brandName && ad.brandName.toLowerCase().includes(term)) ||
      (ad.description && ad.description.toLowerCase().includes(term))
    );
  });

  // Render ad item
  const renderAdItem = (ad) => (
    <ListItem 
      key={ad.id} 
      divider
      sx={{
        cursor: ad.status === "sold" ? "pointer" : "default",
        "&:hover": ad.status === "sold" ? { backgroundColor: "action.hover" } : {}
      }}
      onClick={ad.status === "sold" ? () => handleSoldItemClick(ad) : undefined}
    >
      <ListItemText
        primary={
          <Typography variant="subtitle1" component="div" fontWeight="medium">
            {ad.itemName || ad.brandName || "Untitled Ad"}
          </Typography>
        }
        secondary={
          <Box component="div">
            <Typography component="div" variant="body2" color="text.secondary">
              Price:{" "}
              {ad.isFreeItem ? "Free" : formatPriceWithSymbol(ad.price, "EUR")}
            </Typography>
            {ad.discount > 0 && (
              <Typography component="div" variant="body2" color="primary" fontWeight="medium">
                After {ad.discount}% discount:{" "}
                {formatPriceWithSymbol(
                  calculateFinalPrice(ad.price, ad.discount),
                  "EUR"
                )}
              </Typography>
            )}
            <Typography component="div" variant="body2" color="text.secondary">
              Created: {formatDate(ad.createdAt)}
            </Typography>
            {ad.status === "sold" && (
              <Box component="div">
                <Typography component="div" variant="body2" color="success.main" fontWeight="medium">
                  Sold on: {formatDate(ad.soldAt)}
                </Typography>
                <Typography component="div" variant="body2" color="text.secondary">
                  Buyer: {ad.buyerDisplayName || "Unknown"} ({ad.buyerEmail || "Unknown"})
                </Typography>
                <Typography component="div" variant="body2" sx={{
                  color: ad.orderStatus === 'delivered' ? 'success.dark' :
                         ad.orderStatus === 'dispatched' ? 'success.main' :
                         'warning.main',
                  fontWeight: 'medium',
                  mt: 1
                }}>
                  Status: {ad.orderStatus === 'delivered' ? `Delivered${ad.deliveredAt ? ` on ${formatDate(ad.deliveredAt)}` : ''}` :
                          ad.orderStatus === 'dispatched' ? `Dispatched${ad.dispatchedAt ? ` on ${formatDate(ad.dispatchedAt)}` : ''}` :
                          `Paid${ad.createdAt ? ` on ${formatDate(ad.createdAt)}` : ''} (Awaiting dispatch)`}
                </Typography>
                {ad.trackingNumber && (
                  <Box component="div" sx={{ mt: 1 }}>
                    <Typography component="div" variant="body2">
                      Tracking Number: {ad.trackingNumber}
                    </Typography>
                    <Button
                      variant="text"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        window.open(`https://t.17track.net/en#nums=${ad.trackingNumber}`, '_blank');
                      }}
                      sx={{ mt: 0.5 }}
                    >
                      Track Package
                    </Button>
                  </Box>
                )}
              </Box>
            )}
          </Box>
        }
      />
      <ListItemSecondaryAction>
        {ad.status === "sold" ? (
          <Box sx={{ display: 'flex', gap: 1 }}>
            {ad.paymentStatus === 'completed' && !ad.isDispatched && !ad.isDelivered && (
              <Button
                variant="contained"
                color="primary"
                size="small"
                onClick={() => {
                  console.log('Dispatch clicked for:', ad);
                  setDispatchOrderId(ad.orderId || ad.id);
                  setDispatchDialogOpen(true);
                }}
              >
                Dispatch
              </Button>
            )}
            <IconButton
              edge="end"
              onClick={() => handleDeleteSoldClick(ad)}
              title="Delete"
            >
              <DeleteIcon />
            </IconButton>
            <IconButton
              edge="end"
              onClick={() => handleRepublishClick(ad)}
              title="Republish"
            >
              <PublishIcon />
            </IconButton>
          </Box>
        ) : (
          <>
            <IconButton edge="end" onClick={() => handleEditAd(ad)} title="Edit">
              <EditIcon />
            </IconButton>
            <IconButton
              edge="end"
              onClick={() => handleStatusChange(ad)}
              title={ad.status === "draft" ? "Publish" : "Unpublish"}
              sx={{ ml: 1 }}
            >
              {ad.status === "draft" ? <PublishIcon /> : <UnpublishedIcon />}
            </IconButton>
            <IconButton
              edge="end"
              onClick={() => handleDeleteClick(ad)}
              title="Delete"
              sx={{ ml: 1 }}
            >
              <DeleteIcon />
            </IconButton>
          </>
        )}
      </ListItemSecondaryAction>
    </ListItem>
  );

  // Update the sold items display in the render section
  const renderSoldItem = (ad) => {
    // Determine if we should show dispatch button
    const showDispatchButton = (ad.paymentStatus === 'completed' || 
                             !ad.orderStatus || 
                             ad.orderStatus === 'pending' || 
                             ad.orderStatus === 'paid') &&
                             ad.orderStatus !== 'delivered';  // Hide button if order is delivered

    // Log after variable is defined
    console.log('Should show dispatch button:', showDispatchButton, 'for order:', ad.id);

    return (
      <Card key={ad.id} sx={{ 
        mb: 2,
        '& .MuiCardContent-root': {
          py: 2,
          px: 2.5,
          '&:last-child': { pb: 2 }
        }
      }}>
        <CardContent>
          <Typography variant="h6" component="div" sx={{ 
            fontSize: '1.1rem',
            mb: 1.5 // Reduced margin
          }}>
            {ad.itemName || ad.brandName}
          </Typography>
          <Box component="div" sx={{ mt: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" component="div" color="text.secondary" sx={{ mb: 0.5 }}>
                  <Box component="span" sx={{ fontWeight: 'bold', mr: 1 }}>Order ID:</Box>
                  {ad.orderId || ad.id}
                </Typography>
                <Typography variant="body2" component="div" sx={{ mb: 0.5 }}>
                  <Box component="span" sx={{ fontWeight: 'bold', mr: 1 }}>Buyer:</Box>
                  {ad.buyerDisplayName || "Unknown"} ({ad.buyerEmail || "Unknown"})
                </Typography>
                {ad.shippingAddress && (
                  <Typography variant="body2" component="div" sx={{ mb: 0.5 }}>
                    <Box component="span" sx={{ fontWeight: 'bold', mr: 1 }}>Shipping:</Box>
                    {ad.shippingAddress}
                  </Typography>
                )}
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" component="div" sx={{ mb: 0.5 }}>
                  <Box component="span" sx={{ fontWeight: 'bold', mr: 1 }}>Quantity:</Box>
                  {ad.quantitySold || ad.quantity || 1} {ad.unit || 'piece(s)'}
                </Typography>
                <Typography variant="body2" component="div" sx={{ mb: 0.5 }}>
                  <Box component="span" sx={{ fontWeight: 'bold', mr: 1 }}>Price:</Box>
                  {formatPriceWithSymbol(ad.price || 0, "EUR")} each
                </Typography>
                <Typography variant="body2" component="div" sx={{ mb: 0.5 }}>
                  <Box component="span" sx={{ fontWeight: 'bold', mr: 1 }}>Total:</Box>
                  {formatPriceWithSymbol((ad.price || 0) * (ad.quantitySold || ad.quantity || 1), "EUR")}
                </Typography>
              </Grid>
            </Grid>

            <Typography variant="body2" component="div" sx={{
              color: ad.orderStatus === 'delivered' ? 'success.dark' :
                     ad.orderStatus === 'dispatched' ? 'success.main' :
                     'warning.main',
              fontWeight: 'medium',
              mt: 1.5,
              mb: 1
            }}>
              Status: {ad.orderStatus === 'delivered' ? `Delivered${ad.deliveredAt ? ` on ${formatDate(ad.deliveredAt)}` : ''}` :
                      ad.orderStatus === 'dispatched' ? `Dispatched${ad.dispatchedAt ? ` on ${formatDate(ad.dispatchedAt)}` : ''}` :
                      `Paid${ad.createdAt ? ` on ${formatDate(ad.createdAt)}` : ''} (Awaiting dispatch)`}
            </Typography>
            {ad.trackingNumber && (
              <Box component="div" sx={{ mt: 1, mb: 1 }}>
                <Typography variant="body2" component="div">
                  <Box component="span" sx={{ fontWeight: 'bold', mr: 1 }}>Tracking:</Box>
                  {ad.trackingNumber}
                  <Button
                    variant="text"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      window.open(`https://t.17track.net/en#nums=${ad.trackingNumber}`, '_blank');
                    }}
                    sx={{ ml: 1 }}
                  >
                    Track
                  </Button>
                </Typography>
              </Box>
            )}
          </Box>

          <Box sx={{ 
            mt: 2,
            pt: 1.5,
            borderTop: '1px solid',
            borderColor: 'divider',
            display: 'flex',
            gap: 1,
            justifyContent: 'flex-end'
          }}>
            {showDispatchButton && (
              <Button
                variant="contained"
                color="primary"
                size="small"
                onClick={() => {
                  console.log('Dispatch clicked for:', ad);
                  setDispatchOrderId(ad.orderId || ad.id);
                  setDispatchDialogOpen(true);
                }}
              >
                Dispatch Order
              </Button>
            )}
            <Button
              variant="outlined"
              color="error"
              size="small"
              onClick={() => handleDeleteSoldClick(ad)}
            >
              Delete
            </Button>
            <Button
              variant="outlined"
              size="small"
              onClick={() => handleRepublishClick(ad)}
            >
              Republish
            </Button>
          </Box>
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ width: "100%", p: 2 }}>
      <Typography variant="h5" gutterBottom fontWeight="bold">
        My Ads
      </Typography>

      {actionSuccess && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {actionSuccess}
        </Alert>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          <Paper sx={{ width: "100%" }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant="fullWidth"
            >
              <Tab label={`Drafts (${ads.draftCount || 0})`} />
              <Tab label={`Active Ads (${ads.publishedCount || 0})`} />
              <Tab label={`Sold (${ads.soldCount || 0})`} />
            </Tabs>

            <Box sx={{ p: 2 }}>
              {tabValue === 0 && (
                <>
                  <Box sx={{ mb: 2 }}>
                    <TextField
                      label="Search Drafts"
                      variant="outlined"
                      size="small"
                      fullWidth
                      value={draftSearchTerm}
                      onChange={e => setDraftSearchTerm(e.target.value)}
                    />
                  </Box>
                  {filteredDrafts.length === 0 ? (
                    <Typography
                      variant="body1"
                      sx={{ p: 2, textAlign: "center" }}
                    >
                      You don't have any draft ads yet.
                    </Typography>
                  ) : (
                    <List>{filteredDrafts.map(renderAdItem)}</List>
                  )}
                </>
              )}

              {tabValue === 1 && (
                <>
                  <Box sx={{ mb: 2 }}>
                    <TextField
                      label="Search Active Ads"
                      variant="outlined"
                      size="small"
                      fullWidth
                      value={searchTerm}
                      onChange={e => setSearchTerm(e.target.value)}
                    />
                  </Box>
                  {filteredPublished.length === 0 ? (
                    <Typography
                      variant="body1"
                      sx={{ p: 2, textAlign: "center" }}
                    >
                      You don't have any published ads yet.
                    </Typography>
                  ) : (
                    <List>{filteredPublished.map(renderAdItem)}</List>
                  )}
                </>
              )}

              {tabValue === 2 && (
                <>
                  <Box sx={{ mb: 2 }}>
                    <TextField
                      label="Search Sold Items"
                      variant="outlined"
                      size="small"
                      fullWidth
                      value={soldSearchTerm}
                      onChange={e => setSoldSearchTerm(e.target.value)}
                    />
                  </Box>
                  {filteredSold.length === 0 ? (
                    <Typography variant="body1" sx={{ p: 2, textAlign: "center" }}>
                      You don't have any sold items yet.
                    </Typography>
                  ) : (
                    <Box>
                      {filteredSold.map(ad => {
                        console.log('Rendering sold item:', {
                          id: ad.id,
                          orderId: ad.orderId,
                          orderStatus: ad.orderStatus,
                          paymentStatus: ad.paymentStatus,
                          isDispatched: ad.isDispatched,
                          isDelivered: ad.isDelivered
                        });

                        return renderSoldItem(ad);
                      })}
                    </Box>
                  )}

                  {/* Delete Sold Confirmation Dialog */}
                  <Dialog open={deleteSoldDialogOpen} onClose={() => setDeleteSoldDialogOpen(false)}>
                    <DialogTitle>Delete Sold Item</DialogTitle>
                    <DialogContent>
                      <DialogContentText>
                        Are you sure you want to delete this sold item? This action cannot be undone.
                      </DialogContentText>
                    </DialogContent>
                    <DialogActions>
                      <Button onClick={() => setDeleteSoldDialogOpen(false)} disabled={actionLoading}>
                        Cancel
                      </Button>
                      <Button
                        onClick={handleDeleteSoldConfirm}
                        color="error"
                        disabled={actionLoading}
                        startIcon={actionLoading ? <CircularProgress size={20} /> : null}
                      >
                        Delete
                      </Button>
                    </DialogActions>
                  </Dialog>

                  {/* Republish Sold Confirmation Dialog */}
                  <Dialog open={republishDialogOpen} onClose={() => setRepublishDialogOpen(false)}>
                    <DialogTitle>Republish Sold Item</DialogTitle>
                    <DialogContent>
                      <DialogContentText>
                        Do you want to republish this sold item as a draft ad?
                      </DialogContentText>
                    </DialogContent>
                    <DialogActions>
                      <Button onClick={() => setRepublishDialogOpen(false)} disabled={actionLoading}>
                        Cancel
                      </Button>
                      <Button
                        onClick={handleRepublishConfirm}
                        color="primary"
                        disabled={actionLoading}
                        startIcon={actionLoading ? <CircularProgress size={20} /> : null}
                      >
                        Republish
                      </Button>
                    </DialogActions>
                  </Dialog>
                </>
              )}
            </Box>
          </Paper>

          {/* Buyer Details Dialog */}
          <Dialog 
            open={buyerDetailsDialogOpen} 
            onClose={handleCloseBuyerDetails}
            maxWidth="md"
            fullWidth
          >
            <DialogTitle>Buyer Details</DialogTitle>
            <DialogContent>
              {buyerDetailsLoading ? (
                <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : selectedSoldItem && (
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Item Information
                        </Typography>
                        <Typography variant="body2">
                          <strong>Order ID:</strong> {selectedSoldItem.orderId || selectedSoldItem.id}
                        </Typography>
                        <Typography variant="body2">
                          <strong>Sold Date:</strong> {formatDate(selectedSoldItem.soldAt)}
                        </Typography>
                        <Typography variant="body2">
                          <strong>Order Status:</strong> {selectedSoldItem.orderStatus || "Pending"}
                        </Typography>
                        <Typography variant="body2">
                          <strong>Payment Status:</strong> {selectedSoldItem.paymentStatus || "Unknown"}
                        </Typography>
                        {selectedSoldItem.trackingNumber && (
                          <Typography variant="body2">
                            <strong>Tracking Number:</strong> {selectedSoldItem.trackingNumber}
                          </Typography>
                        )}
                        {selectedSoldItem.buyerInfo && (
                          <>
                            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                              Buyer Information
                            </Typography>
                            <Typography variant="body2">
                              <strong>Name:</strong> {selectedSoldItem.buyerDisplayName || "Not provided"}
                            </Typography>
                            <Typography variant="body2">
                              <strong>Email:</strong> {selectedSoldItem.buyerEmail || "Not provided"}
                            </Typography>
                            <Typography variant="body2" sx={{ mt: 1 }}>
                              <strong>Shipping Address:</strong><br />
                              {selectedSoldItem.shippingAddress || "Not provided"}
                            </Typography>
                            <Typography variant="body2" sx={{ mt: 2 }}>
                              <strong>Order Details:</strong>
                            </Typography>
                            <Typography variant="body2">
                              <strong>Quantity Purchased:</strong> {selectedSoldItem.quantitySold || selectedSoldItem.quantity || 1} {selectedSoldItem.unit || 'piece(s)'}
                            </Typography>
                            <Typography variant="body2">
                              <strong>Price per Item:</strong> {formatPriceWithSymbol(selectedSoldItem.price || 0, "EUR")}
                            </Typography>
                            <Typography variant="body2">
                              <strong>Total Amount:</strong> {formatPriceWithSymbol((selectedSoldItem.price || 0) * (selectedSoldItem.quantitySold || selectedSoldItem.quantity || 1), "EUR")}
                            </Typography>
                            <Typography variant="body2" sx={{ mt: 1 }}>
                              <strong>Location:</strong>{" "}
                              {selectedSoldItem.buyerInfo?.state && (
                                typeof selectedSoldItem.buyerInfo.state === 'object'
                                  ? selectedSoldItem.buyerInfo.state.name || 'Not provided'
                                  : selectedSoldItem.buyerInfo.state
                              )}
                              {selectedSoldItem.buyerInfo?.state && selectedSoldItem.buyerInfo?.country && ', '}
                              {selectedSoldItem.buyerInfo?.country && (
                                typeof selectedSoldItem.buyerInfo.country === 'object'
                                  ? selectedSoldItem.buyerInfo.country.name || 'Not provided'
                                  : selectedSoldItem.buyerInfo.country
                              )}
                            </Typography>
                          </>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseBuyerDetails}>Close</Button>
            </DialogActions>
          </Dialog>

          {/* Delete Confirmation Dialog */}
          <Dialog open={deleteDialogOpen} onClose={handleDeleteCancel}>
            <DialogTitle>Delete Ad</DialogTitle>
            <DialogContent>
              <DialogContentText>
                Are you sure you want to delete this ad? This action cannot be
                undone.
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleDeleteCancel} disabled={actionLoading}>
                Cancel
              </Button>
              <Button
                onClick={handleDeleteConfirm}
                color="error"
                disabled={actionLoading}
                startIcon={
                  actionLoading ? <CircularProgress size={20} /> : null
                }
              >
                Delete
              </Button>
            </DialogActions>
          </Dialog>

          {/* Dispatch Dialog */}
          <Dialog
            open={dispatchDialogOpen}
            onClose={() => {
              setDispatchDialogOpen(false);
              setTrackingNumber('');
            }}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>Dispatch Order</DialogTitle>
            <DialogContent>
              <DialogContentText sx={{ mb: 2 }}>
                Please confirm that you want to dispatch this order. You can optionally add a tracking number.
              </DialogContentText>
              <TextField
                label="Tracking Number (Optional)"
                value={trackingNumber}
                onChange={(e) => setTrackingNumber(e.target.value)}
                fullWidth
                margin="dense"
                variant="outlined"
                placeholder="Enter tracking number if available"
              />
            </DialogContent>
            <DialogActions>
              <Button 
                onClick={() => {
                  setDispatchDialogOpen(false);
                  setTrackingNumber('');
                }}
                disabled={actionLoading}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={() => dispatchOrder(dispatchOrderId, trackingNumber)}
                disabled={actionLoading}
                startIcon={actionLoading ? <CircularProgress size={20} /> : null}
              >
                Confirm Dispatch
              </Button>
            </DialogActions>
          </Dialog>
        </>
      )}

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default Ads;

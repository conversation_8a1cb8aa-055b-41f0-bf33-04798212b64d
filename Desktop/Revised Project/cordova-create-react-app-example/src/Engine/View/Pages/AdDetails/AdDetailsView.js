import React, { useState, useEffect } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Breadcrumbs,
  Link,
  Divider,
  Button,
  Chip,
  CircularProgress,
  Alert,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import EmailIcon from "@mui/icons-material/Email";
import HomeIcon from "@mui/icons-material/Home";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";

// Import VariantSelector component
import VariantSelector from "../../Components/VariantSelector/VariantSelector";

// Import components and styles
import PhotoGallery from "../../Components/PhotoGallery/PhotoGallery";
import {
  containerStyles,
  breadcrumbStyles,
  contentContainerStyles,
  galleryContainerStyles,
  detailsContainerStyles,
  titleStyles,
  priceContainerStyles,
  finalPriceStyles,
  originalPriceStyles,
  discountBadgeStyles,
  sectionTitleStyles,
  descriptionStyles,
  specificationsStyles,
  specItemStyles,
  specLabelStyles,
  specValueStyles,
  sellerInfoStyles,
  actionButtonsStyles,
  addToCartButtonStyles,
  contactSellerButtonStyles,
} from "../../../Model/AdDetails/AdDetailsStyle";

// Import controller functions
import { getAdDetails } from "../../../Controller/Ads/AdDetailsController";
import {
  calculateFinalPrice,
  formatPriceWithSymbol,
} from "../../../Utils/CurrencyUtils";

// Import cart controller
import { addToCart } from "../../../Controller/Cart/CartController";

/**
 * AdDetailsView component for displaying detailed information about an ad
 */
const AdDetailsView = () => {
  const { adId } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  // State
  const [adData, setAdData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [selectedVariants, setSelectedVariants] = useState({});

  // Get ad data from location state or fetch it
  useEffect(() => {
    const fetchAdData = async () => {
      setLoading(true);
      setError("");

      try {
        // Check if we have ad data in location state
        if (location.state?.adData) {
          setAdData(location.state.adData);
          setLoading(false);
          return;
        }

        // Otherwise fetch from Firebase
        const result = await getAdDetails(adId);
        if (result.success) {
          setAdData(result.data);
        } else {
          setError(result.message || "Failed to load ad details");
        }
      } catch (err) {
        console.error("Error loading ad details:", err);
        setError("An error occurred while loading ad details");
      } finally {
        setLoading(false);
      }
    };

    fetchAdData();
  }, [adId, location.state]);

  // Calculate final price
  const finalPrice = adData
    ? calculateFinalPrice(adData.price, adData.discount, adData.isFreeItem)
    : 0;

  // State for login prompt
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);

  // Handle variant selection
  const handleVariantChange = (variants) => {
    setSelectedVariants(variants);
  };

  // Handle add to cart
  const handleAddToCart = () => {
    if (!adData) return;

    // Check if variants are required but not selected
    if (adData.hasVariants) {
      // If sizes are available but not selected
      if (adData.sizes && adData.sizes.length > 0 && !selectedVariants.size) {
        console.error("Please select a size");
        return;
      }

      // If colors are available but not selected
      if (
        adData.colors &&
        adData.colors.length > 0 &&
        !selectedVariants.color
      ) {
        console.error("Please select a color");
        return;
      }
    }

    // Add item to cart with final price and ensure currency is EUR
    // Explicitly set quantity to 1 to ensure we don't add all available stock
    const cartItem = {
      ...adData,
      finalPrice: finalPrice,
      currency: "EUR", // Always use EUR currency
      quantity: 1, // Explicitly set quantity to 1
      // Include selected variants if any
      ...(adData.hasVariants && {
        selectedVariants: {
          size: selectedVariants.size || null,
          color: selectedVariants.color || null,
        },
      }),
    };

    const result = addToCart(cartItem);

    if (result.success) {
      // Show success message or notification
      console.log("Added to cart:", adId);
      setShowLoginPrompt(false); // Hide login prompt if it was showing
    } else {
      // Check if login is required
      if (result.requiresLogin) {
        // Show login prompt
        setShowLoginPrompt(true);
      } else {
        // Show error message
        console.error("Failed to add to cart:", result.message);
      }
    }
  };

  // Handle navigate to login
  const handleNavigateToLogin = () => {
    navigate("/login", { state: { returnUrl: location.pathname } });
  };

  // Handle contact seller
  const handleContactSeller = () => {
    console.log("Contact seller for ad:", adId);
    // Implement contact functionality here
  };

  // Navigate back to home
  const handleBackToHome = () => {
    navigate("/");
  };

  // Format date to show relative time
  const formatDate = (timestamp) => {
    if (!timestamp) return "Unknown date";

    const now = new Date();
    const date = new Date(timestamp);
    const diffTime = now - date;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffTime / (1000 * 60));

    if (diffMinutes < 60) {
      return diffMinutes <= 1 ? "Just now" : `${diffMinutes} minutes ago`;
    } else if (diffHours < 24) {
      return diffHours === 1 ? "1 hour ago" : `${diffHours} hours ago`;
    } else if (diffDays < 7) {
      return diffDays === 1 ? "Yesterday" : `${diffDays} days ago`;
    } else if (diffDays < 30) {
      const diffWeeks = Math.floor(diffDays / 7);
      return diffWeeks === 1 ? "1 week ago" : `${diffWeeks} weeks ago`;
    } else if (diffDays < 365) {
      const diffMonths = Math.floor(diffDays / 30);
      return diffMonths === 1 ? "1 month ago" : `${diffMonths} months ago`;
    } else {
      const diffYears = Math.floor(diffDays / 365);
      return diffYears === 1 ? "1 year ago" : `${diffYears} years ago`;
    }
  };

  // Check if listing is new (less than 3 days old)
  const isNewListing = (timestamp) => {
    if (!timestamp) return false;

    const listingDate = new Date(timestamp);
    const currentDate = new Date();
    const diffTime = currentDate - listingDate;
    const diffDays = diffTime / (1000 * 60 * 60 * 24);

    return diffDays < 3;
  };

  // Render loading state
  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          minHeight: "50vh",
          padding: "24px",
        }}
      >
        <CircularProgress size={60} thickness={4} />
        <Typography variant="h6" sx={{ mt: 3, fontWeight: 500 }}>
          Loading ad details...
        </Typography>
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Box sx={{ padding: "24px" }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button
          variant="outlined"
          startIcon={<HomeIcon />}
          onClick={handleBackToHome}
        >
          Back to Home
        </Button>
      </Box>
    );
  }

  // Render no data state
  if (!adData) {
    return (
      <Box sx={{ padding: "24px" }}>
        <Alert severity="info" sx={{ mb: 2 }}>
          No ad details found
        </Alert>
        <Button
          variant="outlined"
          startIcon={<HomeIcon />}
          onClick={handleBackToHome}
        >
          Back to Home
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={containerStyles(isMobile)}>
      {/* Breadcrumbs */}
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={breadcrumbStyles}
      >
        <Link
          underline="hover"
          color="inherit"
          onClick={handleBackToHome}
          sx={{ cursor: "pointer", display: "flex", alignItems: "center" }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="small" />
          Home
        </Link>
        <Typography color="text.primary">
          {adData.brandName || adData.itemName || "Ad Details"}
        </Typography>
      </Breadcrumbs>

      {/* Main content */}
      <Box sx={contentContainerStyles(isMobile)}>
        {/* Photo Gallery */}
        <Box sx={galleryContainerStyles(isMobile)}>
          <PhotoGallery photos={adData.photos || []} isMobile={isMobile} />
        </Box>

        {/* Details */}
        <Box sx={detailsContainerStyles(isMobile)}>
          {/* Title */}
          <Typography sx={titleStyles}>
            {adData.brandName || adData.itemName || "Untitled Item"}
          </Typography>

          <Divider />

          {/* Price and New Listing Badge */}
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Box sx={priceContainerStyles}>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Typography sx={finalPriceStyles}>
                  {finalPrice === 0 || adData.isFreeItem
                    ? "Free Item"
                    : formatPriceWithSymbol(finalPrice, "EUR")}
                </Typography>
                {adData.discount > 0 && (
                  <>
                    <Typography sx={originalPriceStyles}>
                      {formatPriceWithSymbol(adData.price, "EUR")}
                    </Typography>
                    <Chip
                      label={`-${adData.discount}%`}
                      size="small"
                      sx={discountBadgeStyles}
                    />
                  </>
                )}
              </Box>
            </Box>

            {/* New Listing Badge */}
            {isNewListing(adData.createdAt) && (
              <Chip
                label="New Listing"
                color="secondary"
                size="small"
                sx={{ ml: 2 }}
              />
            )}

            {/* Free Shipping Badge */}
            {(adData.shippingMethod === "Free Shipping" ||
              adData.shippingPayer === "seller") && (
              <Chip
                label="Free Shipping"
                color="success"
                size="small"
                sx={{ ml: 2 }}
              />
            )}
          </Box>

          {/* Variant Selector - Only show if item has variants */}
          {adData.hasVariants &&
            (adData.sizes?.length > 0 || adData.colors?.length > 0) && (
              <VariantSelector
                sizes={adData.sizes || []}
                colors={adData.colors || []}
                onVariantChange={handleVariantChange}
                selectedVariants={selectedVariants}
              />
            )}

          {/* Action Buttons - Moved up as requested */}
          <Box sx={actionButtonsStyles(isMobile)}>
            {/* Check if item is out of stock */}
            {adData.stockStatus === "Out of Stock" ||
            adData.quantityInStock <= 0 ||
            adData.quantity <= 0 ? (
              <>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<ShoppingCartIcon />}
                  fullWidth
                  disabled
                  sx={{
                    ...addToCartButtonStyles(isMobile),
                    opacity: 0.7,
                    backgroundColor: "rgba(0, 0, 0, 0.12)",
                    color: "rgba(0, 0, 0, 0.26)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.12)",
                    },
                  }}
                >
                  Out of Stock
                </Button>
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<EmailIcon />}
                  fullWidth
                  onClick={handleContactSeller}
                  sx={contactSellerButtonStyles(isMobile)}
                >
                  Contact Seller
                </Button>
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ mt: 1, display: "block", textAlign: "center" }}
                >
                  This item is currently out of stock. Contact the seller for
                  inquiries.
                </Typography>
              </>
            ) : (
              <>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<ShoppingCartIcon />}
                  fullWidth
                  onClick={handleAddToCart}
                  sx={addToCartButtonStyles(isMobile)}
                >
                  Add to Cart
                </Button>
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<EmailIcon />}
                  fullWidth
                  onClick={handleContactSeller}
                  sx={contactSellerButtonStyles(isMobile)}
                >
                  Contact Seller
                </Button>
                {adData.quantityInStock && (
                  <Typography
                    variant="caption"
                    color="success.main"
                    sx={{ mt: 1, display: "block", textAlign: "center" }}
                  >
                    {adData.quantityInStock}{" "}
                    {adData.quantityInStock === 1 ? "item" : "items"} in stock
                  </Typography>
                )}
              </>
            )}
          </Box>

          {/* Login Prompt */}
          {showLoginPrompt && (
            <Alert
              severity="info"
              sx={{ mt: 2 }}
              action={
                <Button
                  color="inherit"
                  size="small"
                  onClick={handleNavigateToLogin}
                >
                  LOGIN
                </Button>
              }
            >
              Please log in to add items to your cart
            </Alert>
          )}

          {/* Seller Info */}
          <Box 
            sx={{
              ...sellerInfoStyles,
              cursor: adData.sellerInfo ? 'pointer' : 'default',
              '&:hover': adData.sellerInfo ? {
                backgroundColor: '#f5f5f5',
                transition: 'background-color 0.2s ease'
              } : {}
            }}
            onClick={adData.sellerInfo ? () => navigate(`/seller/${adData.userId}`) : undefined}
          >
         
            {adData.sellerInfo ? (
              <>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {adData.sellerInfo.profilePicture ? (
                    <img 
                      src={adData.sellerInfo.profilePicture} 
                      alt={adData.sellerInfo.displayName}
                      style={{
                        width: '50px',
                        height: '50px',
                        borderRadius: '50%',
                        marginRight: '16px',
                        objectFit: 'cover'
                      }}
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'flex';
                      }}
                    />
                  ) : (
                    <Box 
                      sx={{ 
                        width: '50px', 
                        height: '50px', 
                        borderRadius: '50%', 
                        bgcolor: 'grey.300',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginRight: '16px'
                      }}
                    >
                      <Typography variant="h6" color="text.secondary">
                        {adData.sellerInfo.displayName?.charAt(0) || '?'}
                      </Typography>
                    </Box>
                  )}
                  <Box>
                    <Typography variant="subtitle1" fontWeight="medium" color="primary">
                      {adData.sellerInfo.displayName || 'Unknown Seller'}
                    </Typography>
                  
                  </Box>
                </Box>
                <Box sx={{ pl: 1 }}>
                 
                
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>Location:</strong> {typeof adData.sellerInfo.location === 'object'
                      ? (adData.sellerInfo.location.name
                          || (adData.sellerInfo.location.country && adData.sellerInfo.location.country.name)
                          || 'Not provided')
                      : adData.sellerInfo.location || 'Not provided'}
                  </Typography>
                  <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Typography variant="body2">
                      <strong>Rating:</strong> {adData.sellerInfo.rating?.toFixed(1) || '0.0'} ⭐
                    </Typography>
                   
                  </Box>
                </Box>
              </>
            ) : (
              <Box sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary">
                  Anonymous Seller
                </Typography>
              </Box>
            )}
          </Box>

          {/* Shipping Information - Moved up after seller info */}
          {(adData.shippingFrom || adData.shippingMethod) && (
            <Box sx={{ mt: 3 }}>
              <Typography sx={sectionTitleStyles}>Shipping Details</Typography>
              <Box sx={specificationsStyles}>
                {adData.shippingFrom && (
                  <Box sx={specItemStyles}>
                    <Typography sx={specLabelStyles}>Ships From</Typography>
                    <Typography sx={specValueStyles}>
                      {adData.shippingFrom}
                    </Typography>
                  </Box>
                )}
                {adData.shippingMethod && (
                  <Box sx={specItemStyles}>
                    <Typography sx={specLabelStyles}>
                      Shipping Method
                    </Typography>
                    <Typography sx={specValueStyles}>
                      {adData.shippingMethod}
                    </Typography>
                  </Box>
                )}
                {adData.estimatedDelivery && (
                  <Box sx={specItemStyles}>
                    <Typography sx={specLabelStyles}>
                      Estimated Delivery
                    </Typography>
                    <Typography
                      sx={specValueStyles}
                      color="primary.main"
                      fontWeight="500"
                    >
                      {adData.estimatedDelivery}
                    </Typography>
                  </Box>
                )}
                {adData.returnOption && (
                  <Box sx={specItemStyles}>
                    <Typography sx={specLabelStyles}>Return Policy</Typography>
                    <Typography sx={specValueStyles}>
                      {adData.returnOption
                        .replace(/_/g, " ")
                        .split(" ")
                        .map(
                          (word) => word.charAt(0).toUpperCase() + word.slice(1)
                        )
                        .join(" ")}
                    </Typography>
                  </Box>
                )}
              </Box>
            </Box>
          )}

          {/* Specifications - Moved down as requested */}
          <Typography sx={sectionTitleStyles}>Specifications</Typography>
          <Box sx={specificationsStyles}>
            {adData.category && (
              <Box sx={specItemStyles}>
                <Typography sx={specLabelStyles}>Category</Typography>
                <Typography sx={specValueStyles}>
                  {typeof adData.category === "object"
                    ? adData.category.name || JSON.stringify(adData.category)
                    : adData.category}
                </Typography>
              </Box>
            )}
            {adData.condition && (
              <Box sx={specItemStyles}>
                <Typography sx={specLabelStyles}>Condition</Typography>
                <Typography sx={specValueStyles}>{adData.condition}</Typography>
              </Box>
            )}
            {/* Vehicle details section - only show if at least one field is present */}
            {(adData.make || adData.model || adData.year) && (
              <>
                {adData.make && (
                  <Box sx={specItemStyles}>
                    <Typography sx={specLabelStyles}>Make</Typography>
                    <Typography sx={specValueStyles}>{adData.make}</Typography>
                  </Box>
                )}
                {adData.model && (
                  <Box sx={specItemStyles}>
                    <Typography sx={specLabelStyles}>Model</Typography>
                    <Typography sx={specValueStyles}>{adData.model}</Typography>
                  </Box>
                )}
                {adData.year && (
                  <Box sx={specItemStyles}>
                    <Typography sx={specLabelStyles}>Year</Typography>
                    <Typography sx={specValueStyles}>{adData.year}</Typography>
                  </Box>
                )}
              </>
            )}
            {adData.sellerType && (
              <Box sx={specItemStyles}>
                <Typography sx={specLabelStyles}>Seller Type</Typography>
                <Typography sx={specValueStyles}>
                  {adData.sellerType}
                </Typography>
              </Box>
            )}
            {adData.createdAt && (
              <Box sx={specItemStyles}>
                <Typography sx={specLabelStyles}>Listed On</Typography>
                <Typography sx={specValueStyles}>
                  {formatDate(adData.createdAt)}
                </Typography>
              </Box>
            )}
          </Box>

          {/* Description - Moved to the end */}
          <Typography sx={sectionTitleStyles}>Description</Typography>
          <Typography sx={descriptionStyles}>
            {adData.description || "No description provided"}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default AdDetailsView;

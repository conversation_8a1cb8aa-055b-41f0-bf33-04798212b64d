import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Box, Tabs, Tab, Grid } from "@mui/material";
import "../../../../Engine/Styles/Css/UserProfile.css";
import "../../../../Engine/Styles/Css/LogoutBtn.css";
import "../../../../Engine/Styles/Css/OvalButton.css";
import Footer from "../../Footer/Footer";
import UserQuickLinks from "./UserQuickLinks";
import UserDetails from "./UserDetails";
import {
  fetchUserData,
  handlePhotoUpload,
} from "../../../Controller/UserService/Profile/UserProfile";

// TabPanel component for tab content
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`profile-tabpanel-${index}`}
      aria-labelledby={`profile-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const UserProfile = React.memo(() => {
  const navigate = useNavigate();
  const [profilePhoto, setProfilePhoto] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [user, setUser] = useState(null);
  const [tabValue, setTabValue] = useState(0);

  // Fetch user data on component mount
  useEffect(() => {
    fetchUserData(setUser, setProfilePhoto, setError, setLoading, navigate);
  }, [navigate]);

  // Photo upload handler - delegates to controller
  const photoUploadHandler = (e) => {
    handlePhotoUpload(
      e,
      user,
      setProfilePhoto,
      setUser,
      setError,
      setLoading,
      navigate
    );
  };

  // Modal handlers - simple UI functions can stay in the view
  const handleModalOpen = () => setIsModalOpen(true);
  const handleModalClose = () => setIsModalOpen(false);

  // Tab change handler
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <div className="container">
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <UserDetails
            user={user}
            profilePhoto={profilePhoto}
            error={error}
            loading={loading}
            isModalOpen={isModalOpen}
            handleModalOpen={handleModalOpen}
            handleModalClose={handleModalClose}
            photoUploadHandler={photoUploadHandler}
            setError={setError}
            setLoading={setLoading}
            setUser={setUser}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <Box sx={{ width: "100%", mt: 3 }}>
            <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                aria-label="profile tabs"
                centered
              >
                <Tab label="Quick Links" />
                <Tab label="Tab 2" />
              </Tabs>
            </Box>
            <TabPanel value={tabValue} index={0}>
              <UserQuickLinks />
            </TabPanel>
            <TabPanel value={tabValue} index={1}>
              Tab 2 Content
            </TabPanel>
          </Box>
        </Grid>
      </Grid>
      <div className="footer">
        <Footer />
      </div>
    </div>
  );
});

export default UserProfile;

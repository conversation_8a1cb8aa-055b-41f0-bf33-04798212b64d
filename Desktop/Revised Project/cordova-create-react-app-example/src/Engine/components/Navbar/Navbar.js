import React, { useState, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import {
  AppBar,
  Toolbar,
  Button,
  IconButton,
  Box,
  useMediaQuery,
  useTheme,
  Avatar,
  Typography,
  Badge,
} from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";
import HomeIcon from "@mui/icons-material/Home";
import PersonIcon from "@mui/icons-material/Person";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";

import { StyledAppBar } from "../../Styles/MaterialUIs/NavbarStyles";
import { auth } from "../../Config/firebase";
import { handleLogout } from "../../Controller/UserService/LoginAndOut/LoginLogics";
import { getCartCount } from "../../Controller/Cart/CartController";
import Search from "../../View/Components/Search/Search";
import CategoryDropdown from "../../View/Components/CategoryDropdown/CategoryDropdown";
import FilterNavigation from "../../View/Components/FilterNavigation/FilterNavigation";
import { storeSelectedCategory } from "../../Controller/Category/CategoryController";
import MobileDrawer from "./MobileDrawer";
import MobileBottomNav from "./MobileBottomNav";
import blankProfileImage from "../../Controller/UserService/Empty-image/blankprofile.png";
import "../../Styles/Css/Navbar.css";

const Navbar = () => {
  const [user, setUser] = useState(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [cartCount, setCartCount] = useState(0);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const navigate = useNavigate();
  const location = useLocation();

  // Check if current page is home
  const isHomePage = location.pathname === "/" || location.pathname === "/home";

  console.log("🏠 Navbar: isHomePage check:", {
    pathname: location.pathname,
    isHomePage,
    willRenderFilterNavigation: isHomePage,
  });

  // Update cart count
  useEffect(() => {
    // Initial cart count
    setCartCount(getCartCount());

    // Listen for cart updates
    const handleCartUpdate = () => {
      setCartCount(getCartCount());
    };

    document.addEventListener("cartUpdated", handleCartUpdate);

    return () => {
      document.removeEventListener("cartUpdated", handleCartUpdate);
    };
  }, []);

  // Check if user is logged in
  useEffect(() => {
    const storedUser = localStorage.getItem("user");
    const storedPhotoURL = localStorage.getItem("userPhotoURL");

    if (storedUser) {
      const parsedUser = JSON.parse(storedUser);
      setUser({
        ...parsedUser,
        photoURL: storedPhotoURL || parsedUser.photoURL,
      });
    }

    const unsubscribe = auth.onAuthStateChanged((currentUser) => {
      if (currentUser) {
        const photoURL =
          currentUser.photoURL || localStorage.getItem("userPhotoURL");

        setUser({
          uid: currentUser.uid,
          email: currentUser.email,
          displayName: currentUser.displayName,
          photoURL: photoURL,
        });

        if (photoURL && !localStorage.getItem("userPhotoURL")) {
          localStorage.setItem("userPhotoURL", photoURL);
        }
      } else {
        setUser(null);
      }
    });

    return () => unsubscribe();
  }, [location.pathname]);

  // Prevent going back to protected pages after logout
  useEffect(() => {
    const handlePopState = (event) => {
      if (
        !user &&
        (location.pathname === "/profile" || location.pathname === "/cart")
      ) {
        navigate("/");
      }
    };

    window.addEventListener("popstate", handlePopState);
    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [user, location.pathname, navigate]);

  // Handle search
  const handleSearch = (searchTerm, results) => {
    console.log("🏠 Navbar: handleSearch called with:", {
      searchTerm,
      resultsCount: results?.length,
    });

    try {
      // Store search term and results in localStorage for Home component to access
      if (searchTerm && results) {
        console.log("🏠 Navbar: Storing search results");
        localStorage.setItem("searchTerm", searchTerm);
        localStorage.setItem("searchResults", JSON.stringify(results));
        localStorage.setItem("originalSearchResults", JSON.stringify(results));
      } else {
        console.log("🏠 Navbar: Clearing search results (preserving filters)");
        // Only clear search-related items, NOT filter-related items
        localStorage.removeItem("searchTerm");
        localStorage.removeItem("searchResults");
        localStorage.removeItem("originalSearchResults");
        localStorage.removeItem("searchCategoryIds");
        localStorage.removeItem("searchFilters");
        // DO NOT remove filter-specific items: filterSort, filterDiscount, filterLocation
      }

      // Force a refresh of the Home component by adding a timestamp
      localStorage.setItem("searchTimestamp", Date.now().toString());

      // Navigate to home page to display results
      if (location.pathname !== "/" && location.pathname !== "/home") {
        navigate("/home");
      }
    } catch (error) {
      console.error("Error handling search:", error);
    }
  };

  // Handle logout
  const handleUserLogout = () => {
    handleLogout(navigate);
    // Redirect to home page after logout
    navigate("/");
  };

  // Toggle drawer
  const toggleDrawer = (open) => {
    setDrawerOpen(open);
  };

  // Render web view navbar
  const renderWebNavbar = () => (
    <StyledAppBar position="static">
      <Toolbar variant="dense">
        <Link to="/home" className="navbar-link">
          <Typography variant="h6" component="div" sx={{ flexGrow: 0, mr: 2 }}>
            AdvertsBay
          </Typography>
        </Link>

        {isHomePage && (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              flexGrow: 1,
              maxWidth: "800px",
            }}
          >
            <CategoryDropdown
              onCategorySelect={(category) => storeSelectedCategory(category)}
            />
            <Box sx={{ flexGrow: 1 }}>
              <Search onSearch={handleSearch} />
            </Box>
          </Box>
        )}

        <Box sx={{ flexGrow: 1 }} />

        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button
            color="inherit"
            component={Link}
            to="/home"
            className="navbar-button"
            startIcon={<HomeIcon />}
            sx={{ mx: 1 }}
          >
            Home
          </Button>

          {user ? (
            <>
              <Button
                color="inherit"
                component={Link}
                to="/cart"
                className="navbar-button"
                startIcon={
                  <Badge
                    badgeContent={cartCount}
                    color="error"
                    max={99}
                    sx={{
                      "& .MuiBadge-badge": {
                        fontSize: "0.6rem",
                        height: "16px",
                        minWidth: "16px",
                        padding: "0 4px",
                      },
                    }}
                  >
                    <ShoppingCartIcon fontSize="small" />
                  </Badge>
                }
                sx={{ mx: 1 }}
              >
                Cart
              </Button>

              <Button
                color="inherit"
                component={Link}
                to="/profile"
                className="navbar-button"
                sx={{ mx: 1 }}
                startIcon={<PersonIcon />}
              >
                Profile
                <Avatar
                  src={user.photoURL || blankProfileImage}
                  alt={user.displayName || "User"}
                  sx={{ width: 32, height: 32, ml: 1 }}
                  className="navbar-user-avatar"
                />
              </Button>
            </>
          ) : (
            <Button
              color="inherit"
              component={Link}
              to="/login"
              className="navbar-button"
              sx={{ mx: 1 }}
            >
              Login
            </Button>
          )}
        </Box>
      </Toolbar>
    </StyledAppBar>
  );

  // Render mobile view navbar
  const renderMobileNavbar = () => (
    <>
      <Box className="navbar-mobile-header">
        <IconButton
          color="inherit"
          aria-label="open drawer"
          edge="start"
          onClick={() => toggleDrawer(true)}
        >
          <MenuIcon />
        </IconButton>

        <Typography
          variant="h6"
          component={Link}
          to="/home"
          sx={{ color: "inherit", textDecoration: "none" }}
        >
          AdvertsBay
        </Typography>

        {user && (
          <IconButton
            color="inherit"
            aria-label="user profile"
            edge="end"
            component={Link}
            to="/profile"
          >
            <Avatar
              src={user.photoURL || blankProfileImage}
              alt={user.displayName || "User"}
              sx={{ width: 32, height: 32 }}
              className="navbar-user-avatar-mobile"
            />
          </IconButton>
        )}
      </Box>

      {isHomePage && (
        <Box
          sx={{ p: 1, display: "flex", alignItems: "center", width: "100%" }}
        >
          <CategoryDropdown
            onCategorySelect={(category) => storeSelectedCategory(category)}
          />
          <Box sx={{ flexGrow: 1 }}>
            <Search onSearch={handleSearch} placeholder="Search products..." />
          </Box>
        </Box>
      )}

      <MobileDrawer
        open={drawerOpen}
        onClose={() => toggleDrawer(false)}
        isLoggedIn={!!user}
        user={user}
        onLogout={handleUserLogout}
      />

      <MobileBottomNav isLoggedIn={!!user} />
    </>
  );

  return (
    <>
      <div className="navbar-container">
        {isMobile ? renderMobileNavbar() : renderWebNavbar()}
      </div>

      {/* Filter Navigation - Only show on home page */}
      {isHomePage && <FilterNavigation />}
    </>
  );
};

export default Navbar;

import React, { useState, useEffect } from "react";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import CircularProgress from "@mui/material/CircularProgress";
import {
  Search,
  SearchIconWrapper,
  StyledInputBase,
} from "../../Styles/MaterialUIs/NavbarStyles";
import { searchPublishedAds } from "../../Controller/Ads/PublishedAdsController";

const SimpleSearchBar = ({ onSearch, placeholder = "Search..." }) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedTerm, setDebouncedTerm] = useState("");
  const [showClearButton, setShowClearButton] = useState(false);
  const [isSearching, setIsSearching] = useState(false);

  // Debounce search term to avoid too many API calls
  useEffect(() => {
    const timerId = setTimeout(() => {
      setDebouncedTerm(searchTerm);
    }, 300); // 300ms delay

    return () => {
      clearTimeout(timerId);
    };
  }, [searchTerm]);

  // Show/hide clear button based on search term
  useEffect(() => {
    setShowClearButton(searchTerm.length > 0);
  }, [searchTerm]);

  // Perform search when debounced term changes
  useEffect(() => {
    const performSearch = async () => {
      if (debouncedTerm.trim()) {
        try {
          setIsSearching(true);
          console.log("Performing search for:", debouncedTerm);
          const result = await searchPublishedAds(debouncedTerm);
          console.log("Full search result:", result);
          if (result.success) {
            console.log("Search results:", result.data.results);
            console.log("Results count:", result.data.results.length);
            // Store search term and results in localStorage
            localStorage.setItem("searchTerm", debouncedTerm);
            localStorage.setItem(
              "searchResults",
              JSON.stringify(result.data.results)
            );
            localStorage.setItem(
              "originalSearchResults",
              JSON.stringify(result.data.results)
            );
            localStorage.setItem("searchTimestamp", Date.now().toString());
            console.log("Stored in localStorage:", {
              searchTerm: localStorage.getItem("searchTerm"),
              searchResults: localStorage.getItem("searchResults"),
            });

            // Call the onSearch callback
            onSearch(debouncedTerm, result.data.results);

            // Force a refresh of the page by dispatching a custom event
            document.dispatchEvent(new CustomEvent("storageUpdated"));
          }
        } catch (error) {
          console.error("Search error:", error);
        } finally {
          setIsSearching(false);
        }
      } else if (debouncedTerm === "") {
        // Clear search results
        localStorage.removeItem("searchTerm");
        localStorage.removeItem("searchResults");
        localStorage.removeItem("originalSearchResults");
        localStorage.setItem("searchTimestamp", Date.now().toString());
        onSearch("", null);

        // Force a refresh of the page by dispatching a custom event
        document.dispatchEvent(new CustomEvent("storageUpdated"));
      }
    };

    performSearch();
  }, [debouncedTerm, onSearch]);

  const handleSearch = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
    }
  };

  const handleClear = () => {
    setSearchTerm("");
  };

  const handleChange = (e) => {
    setSearchTerm(e.target.value);
  };

  return (
    <Search>
      <SearchIconWrapper style={{ cursor: "pointer" }}>
        {isSearching ? (
          <CircularProgress size={20} color="inherit" />
        ) : (
          <SearchIcon />
        )}
      </SearchIconWrapper>
      <StyledInputBase
        placeholder={placeholder}
        inputProps={{ "aria-label": "search" }}
        value={searchTerm}
        onChange={handleChange}
        onKeyDown={handleSearch}
        endAdornment={
          showClearButton && (
            <div
              onClick={handleClear}
              style={{
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
                padding: "0 8px",
              }}
            >
              <ClearIcon fontSize="small" />
            </div>
          )
        }
      />
    </Search>
  );
};

export default SimpleSearchBar;

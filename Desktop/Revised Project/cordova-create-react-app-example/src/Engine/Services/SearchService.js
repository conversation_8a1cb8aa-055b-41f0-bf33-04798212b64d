/**
 * SearchService.js
 * Service for handling search functionality with Firebase
 */
import { database } from "../Config/firebase";

/**
 * Search for published ads in Firebase
 * @param {string} query - The search query
 * @param {Object} options - Search options (limit, filters, etc.)
 * @returns {Promise<Object>} - Result object with search results
 */
export const searchPublishedAds = async (query, options = {}) => {
  try {
    console.log(`Searching for published ads with query: "${query}"`);
    
    // Default options
    const {
      limit = 50,
      sortBy = "createdAt",
      sortDirection = "desc",
      categoryFilter = null,
    } = options;

    // Reference to the publishedAds collection
    const adsRef = database.ref("publishedAds");
    
    // Get all published ads
    const snapshot = await adsRef.once("value");
    const adsData = snapshot.val() || {};
    
    // Convert to array and filter by status
    let ads = Object.keys(adsData)
      .map((adId) => ({
        ...adsData[adId],
        id: adId,
      }))
      .filter((ad) => ad.status === "published");
    
    // Apply search query if provided
    if (query && query.trim() !== "") {
      const normalizedQuery = query.toLowerCase().trim();
      
      ads = ads.filter((ad) => {
        // Search in title
        const titleMatch = ad.title && ad.title.toLowerCase().includes(normalizedQuery);
        
        // Search in description
        const descriptionMatch = ad.description && ad.description.toLowerCase().includes(normalizedQuery);
        
        // Search in brand name
        const brandMatch = ad.brandName && ad.brandName.toLowerCase().includes(normalizedQuery);
        
        // Search in category name
        const categoryMatch = 
          (ad.category && typeof ad.category === 'object' && ad.category.name && 
           ad.category.name.toLowerCase().includes(normalizedQuery)) ||
          (ad.category && typeof ad.category === 'string' && 
           ad.category.toLowerCase().includes(normalizedQuery));
        
        // Return true if any field matches
        return titleMatch || descriptionMatch || brandMatch || categoryMatch;
      });
    }
    
    // Apply category filter if provided
    if (categoryFilter) {
      ads = ads.filter((ad) => {
        if (typeof ad.category === 'object') {
          return ad.category.id === categoryFilter || ad.category.parentId === categoryFilter;
        } else {
          return ad.categoryId === categoryFilter || ad.parentCategoryId === categoryFilter;
        }
      });
    }
    
    // Sort results
    ads.sort((a, b) => {
      const valueA = a[sortBy] || 0;
      const valueB = b[sortBy] || 0;
      
      if (sortDirection === "asc") {
        return valueA > valueB ? 1 : -1;
      } else {
        return valueA < valueB ? 1 : -1;
      }
    });
    
    // Apply limit
    if (limit > 0) {
      ads = ads.slice(0, limit);
    }
    
    console.log(`Found ${ads.length} published ads matching query`);
    
    return {
      success: true,
      ads,
      total: ads.length,
      query,
    };
  } catch (error) {
    console.error("Error searching published ads:", error);
    return {
      success: false,
      message: error.message || "Failed to search ads",
      ads: [],
      total: 0,
    };
  }
};

/**
 * Get real-time search results for published ads
 * @param {string} query - The search query
 * @param {function} callback - Callback function to receive results
 * @param {Object} options - Search options
 * @returns {function} - Unsubscribe function
 */
export const getRealtimeSearchResults = (query, callback, options = {}) => {
  // Reference to the publishedAds collection
  const adsRef = database.ref("publishedAds");
  
  // Listen for changes
  const unsubscribe = adsRef.on("value", (snapshot) => {
    try {
      const adsData = snapshot.val() || {};
      
      // Convert to array and filter by status
      let ads = Object.keys(adsData)
        .map((adId) => ({
          ...adsData[adId],
          id: adId,
        }))
        .filter((ad) => ad.status === "published");
      
      // Apply search query if provided
      if (query && query.trim() !== "") {
        const normalizedQuery = query.toLowerCase().trim();
        
        ads = ads.filter((ad) => {
          // Search in title
          const titleMatch = ad.title && ad.title.toLowerCase().includes(normalizedQuery);
          
          // Search in description
          const descriptionMatch = ad.description && ad.description.toLowerCase().includes(normalizedQuery);
          
          // Search in brand name
          const brandMatch = ad.brandName && ad.brandName.toLowerCase().includes(normalizedQuery);
          
          // Search in category name
          const categoryMatch = 
            (ad.category && typeof ad.category === 'object' && ad.category.name && 
             ad.category.name.toLowerCase().includes(normalizedQuery)) ||
            (ad.category && typeof ad.category === 'string' && 
             ad.category.toLowerCase().includes(normalizedQuery));
          
          // Return true if any field matches
          return titleMatch || descriptionMatch || brandMatch || categoryMatch;
        });
      }
      
      // Apply other options (limit, sort, category filter)
      const {
        limit = 50,
        sortBy = "createdAt",
        sortDirection = "desc",
        categoryFilter = null,
      } = options;
      
      // Apply category filter if provided
      if (categoryFilter) {
        ads = ads.filter((ad) => {
          if (typeof ad.category === 'object') {
            return ad.category.id === categoryFilter || ad.category.parentId === categoryFilter;
          } else {
            return ad.categoryId === categoryFilter || ad.parentCategoryId === categoryFilter;
          }
        });
      }
      
      // Sort results
      ads.sort((a, b) => {
        const valueA = a[sortBy] || 0;
        const valueB = b[sortBy] || 0;
        
        if (sortDirection === "asc") {
          return valueA > valueB ? 1 : -1;
        } else {
          return valueA < valueB ? 1 : -1;
        }
      });
      
      // Apply limit
      if (limit > 0) {
        ads = ads.slice(0, limit);
      }
      
      // Call the callback with results
      callback({
        success: true,
        ads,
        total: ads.length,
        query,
      });
    } catch (error) {
      console.error("Error in real-time search:", error);
      callback({
        success: false,
        message: error.message || "Failed to search ads",
        ads: [],
        total: 0,
      });
    }
  });
  
  // Return unsubscribe function
  return () => {
    adsRef.off("value", unsubscribe);
  };
};

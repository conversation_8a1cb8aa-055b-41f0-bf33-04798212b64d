const { database } = require('../../config/firebase');

/**
 * Update item quantity after purchase
 * This function decreases the quantity of an item by the purchased amount
 * and marks it as "Out of Stock" when the quantity reaches 0
 */
exports.updateQuantityAfterPurchase = async (req, res) => {
  try {
    console.log(
      "updateQuantityAfterPurchase called with request body:",
      JSON.stringify(req.body, null, 2)
    );
    const { adId, purchasedQuantity = 1 } = req.body;

    if (!adId) {
      return res.status(400).json({
        success: false,
        message: "Ad ID is required",
      });
    }

    // Get the ad data from the database
    const adRef = database.ref(`publishedAds/${adId}`);
    const adSnapshot = await adRef.once("value");
    const adData = adSnapshot.val();

    if (!adData) {
      return res.status(404).json({
        success: false,
        message: `Ad not found with ID: ${adId}`,
      });
    }

    console.log(`Found ad data for ${adId}:`, adData);

    // Check if the quantity field exists
    if (
      typeof adData.quantity !== "number" &&
      typeof adData.quantity !== "string"
    ) {
      return res.status(400).json({
        success: false,
        message: "Ad does not have a valid quantity field",
      });
    }

    // Convert quantity to number
    let currentQuantity =
      typeof adData.quantity === "string"
        ? parseInt(adData.quantity, 10)
        : adData.quantity;

    // If NaN, treat as 1
    if (isNaN(currentQuantity)) {
      currentQuantity = 1;
    }

    // Calculate new quantity
    const newQuantity = Math.max(0, currentQuantity - purchasedQuantity);
    console.log(
      `Current quantity: ${currentQuantity}, Purchased: ${purchasedQuantity}, New quantity: ${newQuantity}`
    );

    // Prepare updates based on the new quantity
    const updates = {
      quantity: newQuantity,
      quantityInStock: newQuantity,
      stockStatus: newQuantity > 0 ? "In Stock" : "Out of Stock",
    };

    // Update in publishedAds - use set with merge to ensure all fields are updated
    await adRef.update(updates);

    // Double-check with a direct update to ensure quantity is updated correctly
    await database.ref(`publishedAds/${adId}/quantity`).set(newQuantity);
    await database.ref(`publishedAds/${adId}/quantityInStock`).set(newQuantity);
    await database
      .ref(`publishedAds/${adId}/stockStatus`)
      .set(newQuantity > 0 ? "In Stock" : "Out of Stock");

    console.log(
      `Successfully updated quantity in publishedAds for item ${adId}`
    );

    // Also update in the seller's ads collection if userId exists
    if (adData.userId) {
      const userAdRef = database.ref(`users/${adData.userId}/ads/${adId}`);
      await userAdRef.update(updates);

      // Double-check with a direct update to ensure quantity is updated correctly
      await database
        .ref(`users/${adData.userId}/ads/${adId}/quantity`)
        .set(newQuantity);
      await database
        .ref(`users/${adData.userId}/ads/${adId}/quantityInStock`)
        .set(newQuantity);
      await database
        .ref(`users/${adData.userId}/ads/${adId}/stockStatus`)
        .set(newQuantity > 0 ? "In Stock" : "Out of Stock");

      console.log(
        `Successfully updated quantity in user's ads collection for item ${adId}`
      );
    }

    return res.status(200).json({
      success: true,
      message:
        newQuantity > 0
          ? `Quantity updated successfully - ${newQuantity} items remaining in stock`
          : "Quantity updated successfully - item is now out of stock",
      adId: adId,
      previousQuantity: currentQuantity,
      newQuantity: newQuantity,
      stockStatus: newQuantity > 0 ? "In Stock" : "Out of Stock",
    });
  } catch (error) {
    console.error("Error updating quantity:", error);
    return res.status(500).json({
      success: false,
      message: "Error updating quantity",
      error: error.message,
    });
  }
};

/**
 * Check Quantity Controller
 * 
 * This controller provides functions to check the available quantity of items in the database.
 */

const { database } = require('../../config/firebase');

/**
 * Check the available quantity of an item
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.checkAvailableQuantity = async (req, res) => {
  try {
    const { adId } = req.params;

    if (!adId) {
      return res.status(400).json({
        success: false,
        message: "Ad ID is required"
      });
    }

    console.log(`Checking available quantity for ad ${adId}`);

    // Get the ad data from the database
    const adSnapshot = await database.ref(`publishedAds/${adId}`).once("value");
    const adData = adSnapshot.val();

    if (!adData) {
      console.log(`Ad ${adId} not found in publishedAds, checking user ads collections`);
      
      // Try to find the ad in user ads collections
      const usersSnapshot = await database.ref("users").once("value");
      const users = usersSnapshot.val();
      let foundInUserAds = false;
      
      if (users) {
        for (const uid in users) {
          if (users[uid].ads && users[uid].ads[adId]) {
            console.log(`Found ad ${adId} in user ${uid}'s ads collection`);
            const userAdData = users[uid].ads[adId];
            
            // Convert to numbers if they're strings
            const quantity = Number(userAdData.quantity || 0);
            const quantityInStock = Number(userAdData.quantityInStock || 0);
            const stockStatus = userAdData.stockStatus || "Out of Stock";
            
            foundInUserAds = true;
            return res.status(200).json({
              success: true,
              adId,
              quantity,
              quantityInStock,
              stockStatus,
              source: `users/${uid}/ads`
            });
          }
        }
      }
      
      if (!foundInUserAds) {
        return res.status(404).json({
          success: false,
          message: `Ad ${adId} not found in any collection`
        });
      }
    }

    // Convert to numbers if they're strings
    const quantity = Number(adData.quantity || 0);
    const quantityInStock = Number(adData.quantityInStock || 0);
    const stockStatus = adData.stockStatus || "In Stock";

    return res.status(200).json({
      success: true,
      adId,
      quantity,
      quantityInStock,
      stockStatus,
      source: "publishedAds"
    });
  } catch (error) {
    console.error(`Error checking quantity: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: `Error checking quantity: ${error.message}`
    });
  }
};

const firebase = require('../../config/firebase');
const database = firebase.database;

/**
 * Get all published ads from all users
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAllPublishedAds = async (req, res) => {
  try {
    console.log("Fetching all published ads from server...");

    // Get all users
    const usersSnapshot = await database.ref("users").once("value");
    const users = usersSnapshot.val();

    if (!users) {
      return res.status(200).json({
        success: true,
        message: "No users found",
        data: { published: [], publishedCount: 0 },
      });
    }

    let publishedAds = [];

    // Loop through each user
    for (const userId in users) {
      // Check if user has ads
      if (users[userId].ads) {
        // Get all ads for this user
        const userAds = users[userId].ads;

        // Filter published ads and add user info
        for (const adId in userAds) {
          const ad = userAds[adId];

          if (ad.status === "published") {
            // Add user info to the ad
            publishedAds.push({
              ...ad,
              id: adId,
              userId: userId,
              userEmail: users[userId].email || ad.userEmail,
              userName: users[userId].username || "Unknown User",
            });
          }
        }
      }
    }

    // Sort ads by updatedAt (newest first)
    publishedAds.sort((a, b) => (b.updatedAt || 0) - (a.updatedAt || 0));

    console.log(`Found ${publishedAds.length} published ads`);

    return res.status(200).json({
      success: true,
      message: "Published ads fetched successfully",
      data: {
        published: publishedAds,
        publishedCount: publishedAds.length,
      },
    });
  } catch (error) {
    console.error("Error fetching published ads:", error);
    return res.status(500).json({
      success: false,
      message: "Error fetching published ads",
      error: error.message,
    });
  }
};

/**
 * Search published ads by keyword
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.searchPublishedAds = async (req, res) => {
  try {
    const { keyword } = req.query;

    if (!keyword || keyword.trim() === "") {
      return res.status(400).json({
        success: false,
        message: "Search keyword is required",
      });
    }

    console.log(`Searching published ads for keyword: ${keyword}`);

    // Get all users
    const usersSnapshot = await database.ref("users").once("value");
    const users = usersSnapshot.val();

    if (!users) {
      return res.status(200).json({
        success: true,
        message: "No users found",
        data: { results: [], resultsCount: 0, searchTerm: keyword },
      });
    }

    let publishedAds = [];

    // Loop through each user
    for (const userId in users) {
      // Check if user has ads
      if (users[userId].ads) {
        // Get all ads for this user
        const userAds = users[userId].ads;

        // Filter published ads and add user info
        for (const adId in userAds) {
          const ad = userAds[adId];

          if (ad.status === "published") {
            // Add user info to the ad
            publishedAds.push({
              ...ad,
              id: adId,
              userId: userId,
              userEmail: users[userId].email || ad.userEmail,
              userName: users[userId].username || "Unknown User",
            });
          }
        }
      }
    }

    const searchTerm = keyword.toLowerCase().trim();

    // Filter ads by keyword in brand name, description, or category
    const searchResults = publishedAds.filter((ad) => {
      // Safely get category as string
      let categoryStr = "";
      if (ad.category) {
        if (typeof ad.category === "string") {
          categoryStr = ad.category;
        } else if (typeof ad.category === "object") {
          // If category is an object, try to get the name property
          categoryStr = ad.category.name || JSON.stringify(ad.category);
        } else {
          // For any other type, convert to string
          categoryStr = String(ad.category);
        }
      }

      return (
        (ad.brandName &&
          typeof ad.brandName === "string" &&
          ad.brandName.toLowerCase().includes(searchTerm)) ||
        (ad.description &&
          typeof ad.description === "string" &&
          ad.description.toLowerCase().includes(searchTerm)) ||
        (categoryStr && categoryStr.toLowerCase().includes(searchTerm))
      );
    });

    // Sort results by updatedAt (newest first)
    searchResults.sort((a, b) => (b.updatedAt || 0) - (a.updatedAt || 0));

    console.log(
      `Found ${searchResults.length} results for keyword: ${keyword}`
    );

    return res.status(200).json({
      success: true,
      message: "Search completed successfully",
      data: {
        results: searchResults,
        resultsCount: searchResults.length,
        searchTerm: keyword,
      },
    });
  } catch (error) {
    console.error("Error searching published ads:", error);
    return res.status(500).json({
      success: false,
      message: "Error searching published ads",
      error: error.message,
    });
  }
};

/**
 * categoryController.js
 * Controller for managing categories in Firebase Realtime Database
 */
const firebase = require("../../config/firebase");
const database = firebase.database;

// Database reference
const categoriesRef = database.ref("categories");

// Get all categories
exports.getAllCategories = async (req, res) => {
  try {
    const snapshot = await categoriesRef.once("value");
    const categories = [];

    snapshot.forEach((childSnapshot) => {
      categories.push({
        id: childSnapshot.key,
        ...childSnapshot.val(),
      });
    });

    return res.status(200).json({
      success: true,
      categories,
    });
  } catch (error) {
    console.error("Error getting categories:", error);
    return res.status(500).json({
      success: false,
      message: "Error getting categories",
      error: error.message,
    });
  }
};

// Add a new category
exports.addCategory = async (req, res) => {
  try {
    const { name, parentId, title, userId } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: "Category name is required",
      });
    }

    const newCategoryRef = categoriesRef.push();
    const newCategory = {
      name,
      parentId: parentId || null,
      title: title || null,
      createdAt: new Date().toISOString(),
      createdBy: userId || "admin",
    };

    await newCategoryRef.set(newCategory);

    return res.status(201).json({
      success: true,
      message: "Category added successfully",
      categoryId: newCategoryRef.key,
    });
  } catch (error) {
    console.error("Error adding category:", error);
    return res.status(500).json({
      success: false,
      message: "Error adding category",
      error: error.message,
    });
  }
};

// Update a category
exports.updateCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, title, userId } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: "Category name is required",
      });
    }

    // Check if category exists
    const categoryRef = categoriesRef.child(id);
    const snapshot = await categoryRef.once("value");

    if (!snapshot.exists()) {
      return res.status(404).json({
        success: false,
        message: "Category not found",
      });
    }

    // Prepare update data
    const updateData = {
      name,
      updatedAt: new Date().toISOString(),
      updatedBy: userId || "admin",
    };

    // Add title if provided
    if (title !== undefined) {
      updateData.title = title;
    }

    // Update the category
    await categoryRef.update(updateData);

    return res.status(200).json({
      success: true,
      message: "Category updated successfully",
    });
  } catch (error) {
    console.error("Error updating category:", error);
    return res.status(500).json({
      success: false,
      message: "Error updating category",
      error: error.message,
    });
  }
};

// Delete a category
exports.deleteCategory = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if category exists
    const categoryRef = categoriesRef.child(id);
    const snapshot = await categoryRef.once("value");

    if (!snapshot.exists()) {
      return res.status(404).json({
        success: false,
        message: "Category not found",
      });
    }

    // Check if there are any subcategories
    const subcategoriesSnapshot = await categoriesRef
      .orderByChild("parentId")
      .equalTo(id)
      .once("value");

    if (subcategoriesSnapshot.exists()) {
      return res.status(400).json({
        success: false,
        message: "Cannot delete category with subcategories",
      });
    }

    // Delete the category
    await categoryRef.remove();

    return res.status(200).json({
      success: true,
      message: "Category deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting category:", error);
    return res.status(500).json({
      success: false,
      message: "Error deleting category",
      error: error.message,
    });
  }
};

// Get subcategories for a parent category
exports.getSubcategories = async (req, res) => {
  try {
    const { parentId } = req.params;

    const snapshot = await categoriesRef
      .orderByChild("parentId")
      .equalTo(parentId)
      .once("value");

    const subcategories = [];

    snapshot.forEach((childSnapshot) => {
      subcategories.push({
        id: childSnapshot.key,
        ...childSnapshot.val(),
      });
    });

    return res.status(200).json({
      success: true,
      subcategories,
    });
  } catch (error) {
    console.error("Error getting subcategories:", error);
    return res.status(500).json({
      success: false,
      message: "Error getting subcategories",
      error: error.message,
    });
  }
};

const firebase = require('../../config/firebase');
const database = firebase.database;

exports.dispatchOrder = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { trackingNumber, userId } = req.body;
    
    if (!userId) {
      throw new Error('User ID is required');
    }

    console.log('Dispatch request:', {
      orderId,
      trackingNumber,
      userId,
      headers: req.headers
    });

    // Try to find the order in different locations
    const orderLocations = [
      `users/${userId}/soldItems/${orderId}`,  // Try user's soldItems first
      `users/${userId}/orders/${orderId}`      // Then user's orders
    ];

    let order = null;
    let orderPath = null;

    for (const location of orderLocations) {
      console.log('Checking location:', location);
      try {
        const snapshot = await database.ref(location).once('value');
        if (snapshot.exists()) {
          order = snapshot.val();
          orderPath = location;
          console.log('Found order at:', location);
          break;
        }
      } catch (error) {
        console.log('Error checking location:', location, error.message);
        continue;
      }
    }

    if (!order) {
      console.log('Order not found in any location');
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    console.log('Found order:', order);

    // Only update dispatch-related fields
    const updateData = {
      orderStatus: 'dispatched',
      dispatchedAt: Date.now(),
      trackingNumber: trackingNumber || '',
      isDispatched: true,
      lastUpdatedAt: Date.now()
    };

    console.log('Updating with data:', updateData);

    const updatePromises = [];

    // Update seller's soldItems for this specific order only
    updatePromises.push(
      database.ref(`users/${userId}/soldItems/${orderId}`).update(updateData)
    );

    // Update buyer's order if we have buyer information
    if (order.buyerId) {
      updatePromises.push(
        database.ref(`users/${order.buyerId}/orders/${orderId}`).update(updateData)
      );
    }

    // Update the specific ad if it exists
    if (order.adId) {
      const adUpdateData = {
        orderStatus: 'dispatched',
        isDispatched: true,
        lastUpdatedAt: Date.now()
      };

      // Update in seller's ads
      updatePromises.push(
        database.ref(`users/${userId}/ads/${order.adId}`).update(adUpdateData)
      );

      // Update in seller's publishedAds
      updatePromises.push(
        database.ref(`users/${userId}/publishedAds/${order.adId}`).update(adUpdateData)
      );
    }

    // Wait for all updates to complete
    await Promise.all(updatePromises);

    console.log('Order dispatched successfully:', orderId);

    res.json({
      success: true,
      message: 'Order dispatched successfully'
    });

  } catch (error) {
    console.error('Error dispatching order:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to dispatch order: ' + error.message
    });
  }
};

exports.getOrderStatus = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { userId } = req.query;
    
    if (!userId) {
      throw new Error('User ID is required');
    }

    console.log('Getting order status:', {
      orderId,
      userId,
      headers: req.headers
    });

    // Try to find the order in different locations
    const orderLocations = [
      `users/${userId}/soldItems/${orderId}`,
      `users/${userId}/orders/${orderId}`
    ];

    let order = null;

    for (const location of orderLocations) {
      console.log('Checking location:', location);
      try {
        const snapshot = await database.ref(location).once('value');
        if (snapshot.exists()) {
          order = snapshot.val();
          console.log('Found order at:', location);
          break;
        }
      } catch (error) {
        console.log('Error checking location:', location, error.message);
        continue;
      }
    }

    if (!order) {
      console.log('Order not found in any location');
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    console.log('Returning order status:', {
      status: order.orderStatus || 'pending',
      paymentStatus: order.paymentStatus || 'completed',
      deliveredAt: order.deliveredAt || null
    });

    res.json({
      success: true,
      status: order.orderStatus || 'pending',
      paymentStatus: order.paymentStatus || 'completed',
      trackingNumber: order.trackingNumber || '',
      deliveredAt: order.deliveredAt || null
    });

  } catch (error) {
    console.error('Error getting order status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get order status: ' + error.message
    });
  }
};

exports.confirmDelivery = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { userId } = req.body; // This is the buyer's ID
    
    if (!userId) {
      throw new Error('User ID is required');
    }

    console.log('Confirm delivery request:', {
      orderId,
      userId,
      headers: req.headers
    });

    // First, get the order details from the buyer's orders
    const orderRef = database.ref(`users/${userId}/orders/${orderId}`);
    const orderSnapshot = await orderRef.once('value');
    
    if (!orderSnapshot.exists()) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    const order = orderSnapshot.val();
    // Get seller's ID from the order - try multiple possible fields
    const sellerId = order.sellerId || order.userId;

    if (!sellerId) {
      console.error('Seller ID not found in order:', order);
      return res.status(400).json({
        success: false,
        message: 'Seller information not found in order'
      });
    }

    // Get buyer information
    const buyerSnapshot = await database.ref(`users/${userId}`).once('value');
    const buyerData = buyerSnapshot.val();
    const buyerEmail = buyerData?.email || 'Unknown';
    const buyerDisplayName = buyerData?.displayName || 'Unknown';

    // Update data with delivery confirmation and buyer info
    const updateData = {
      orderStatus: 'delivered',
      isDelivered: true,
      deliveredAt: Date.now(),
      lastUpdatedAt: Date.now(),
      buyerEmail: buyerEmail,
      buyerDisplayName: buyerDisplayName,
      buyerId: userId
    };

    console.log('Updating with delivery confirmation:', updateData);
    console.log('Seller ID:', sellerId);
    console.log('Order ID:', orderId);

    const updatePromises = [];

    // Update buyer's order
    updatePromises.push(
      database.ref(`users/${userId}/orders/${orderId}`).update(updateData)
    );

    // Update seller's soldItems
    updatePromises.push(
      database.ref(`users/${sellerId}/soldItems/${orderId}`).update(updateData)
    );

    // Update the ad if it exists
    if (order.adId) {
      // First get the current ad data to check quantity
      const adSnapshot = await database.ref(`users/${sellerId}/ads/${order.adId}`).once('value');
      const adData = adSnapshot.val();
      
      if (!adData) {
        console.log(`Ad ${order.adId} not found in seller's ads`);
      } else {
        const currentQuantity = adData.quantityInStock || 0;
        const purchaseQuantity = order.quantity || 1;
        const newQuantity = Math.max(0, currentQuantity - purchaseQuantity);
        
        const adUpdateData = {
          orderStatus: 'delivered',
          isDelivered: true,
          deliveredAt: Date.now(),
          lastUpdatedAt: Date.now(),
          quantityInStock: newQuantity,
          stockStatus: newQuantity > 0 ? "In Stock" : "Out of Stock",
          lastPurchaseInfo: {
            buyerEmail: buyerEmail,
            buyerDisplayName: buyerDisplayName,
            buyerId: userId,
            purchaseDate: Date.now(),
            deliveryDate: Date.now(),
            quantityPurchased: purchaseQuantity
          }
        };

        // Update in seller's ads
        updatePromises.push(
          database.ref(`users/${sellerId}/ads/${order.adId}`).update(adUpdateData)
        );

        // Update in seller's publishedAds
        updatePromises.push(
          database.ref(`users/${sellerId}/publishedAds/${order.adId}`).update(adUpdateData)
        );
      }
    }

    // Wait for all updates to complete
    const results = await Promise.allSettled(updatePromises);
    
    // Check for any failures
    const failures = results.filter(result => result.status === 'rejected');
    if (failures.length > 0) {
      console.error('Some updates failed:', failures);
      failures.forEach(failure => {
        console.error('Update failure reason:', failure.reason);
      });
    }

    console.log('Delivery confirmed successfully:', orderId);
    console.log('Updated locations:', {
      buyerOrders: `users/${userId}/orders/${orderId}`,
      sellerSoldItems: `users/${sellerId}/soldItems/${orderId}`,
      sellerAds: order.adId ? `users/${sellerId}/ads/${order.adId}` : null,
      sellerPublishedAds: order.adId ? `users/${sellerId}/publishedAds/${order.adId}` : null
    });

    res.json({
      success: true,
      message: 'Delivery confirmed successfully'
    });

  } catch (error) {
    console.error('Error confirming delivery:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to confirm delivery: ' + error.message
    });
  }
};

// Get buyer's orders
exports.getBuyerOrders = async (req, res) => {
  try {
    const userId = req.user.uid; // From auth middleware
    
    console.log('Getting buyer orders for user:', userId);
    
    const ordersRef = database.ref(`users/${userId}/orders`);
    const snapshot = await ordersRef.once('value');
    const ordersData = snapshot.val() || {};

    // Convert to array and format
    const orders = Object.entries(ordersData).map(([id, order]) => {
      // Get the activity timestamp based on status
      let activityTimestamp;
      if (order.orderStatus === 'dispatched') {
        // For dispatched orders, use dispatchedAt time and add highest priority boost
        activityTimestamp = (order.dispatchedAt || 0) + 2000000; // Increased boost for dispatched
      } else if (order.orderStatus === 'delivered') {
        // For delivered orders, use deliveredAt time with a smaller boost to keep them high but below dispatched
        const deliveryTime = order.deliveredAt || 0;
        const timeSinceDelivery = Date.now() - deliveryTime;
        // Keep delivered orders prominent for 7 days after delivery
        if (timeSinceDelivery < 7 * 24 * 60 * 60 * 1000) {
          activityTimestamp = deliveryTime + 1000000; // Smaller boost than dispatched
        } else {
          activityTimestamp = deliveryTime;
        }
      } else {
        // For paid/pending orders, use created or paid time
        activityTimestamp = order.paidAt || order.createdAt || 0;
      }

      return {
        ...order,
        id,
        activityTimestamp,
        formattedDate: new Date(order.createdAt || Date.now()).toLocaleDateString()
      };
    }).sort((a, b) => {
      // Sort by activity timestamp (highest first)
      return b.activityTimestamp - a.activityTimestamp;
    });

    console.log(`Found ${orders.length} orders for buyer:`, userId);

    res.json({
      success: true,
      orders
    });

  } catch (error) {
    console.error('Error getting buyer orders:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get buyer orders: ' + error.message
    });
  }
};

// Get seller's orders
exports.getSellerOrders = async (req, res) => {
  try {
    const userId = req.user.uid; // From auth middleware
    
    console.log('Getting seller orders for user:', userId);
    
    const soldItemsRef = database.ref(`users/${userId}/soldItems`);
    const snapshot = await soldItemsRef.once('value');
    const ordersData = snapshot.val() || {};

    // Convert to array and format
    const orders = Object.entries(ordersData).map(([id, order]) => ({
      ...order,
      id,
      formattedDate: new Date(order.createdAt || Date.now()).toLocaleDateString()
    }));

    console.log(`Found ${orders.length} orders for seller:`, userId);

    res.json({
      success: true,
      orders
    });

  } catch (error) {
    console.error('Error getting seller orders:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get seller orders: ' + error.message
    });
  }
};

// Get single order
exports.getOrder = async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user.uid;

    console.log('Getting order details:', { orderId, userId });

    // Try to find the order in different locations
    const orderLocations = [
      `users/${userId}/orders/${orderId}`,     // Buyer's orders
      `users/${userId}/soldItems/${orderId}`,  // Seller's orders
      `orders/${orderId}`                      // Global orders
    ];

    let order = null;

    for (const location of orderLocations) {
      console.log('Checking location:', location);
      const snapshot = await database.ref(location).once('value');
      if (snapshot.exists()) {
        order = {
          ...snapshot.val(),
          id: orderId,
          formattedDate: new Date(snapshot.val().createdAt || Date.now()).toLocaleDateString()
        };
        console.log('Found order at:', location);
        break;
      }
    }

    if (!order) {
      console.log('Order not found');
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      order
    });

  } catch (error) {
    console.error('Error getting order:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get order: ' + error.message
    });
  }
}; 
const { Resend } = require('resend');
const resend = new Resend(process.env.RESEND_API_KEY);

exports.sendOrderConfirmation = async (buyerEmail, sellerEmail, orderData) => {
  if (!process.env.RESEND_API_KEY) {
    throw new Error('RESEND_API_KEY is not set in environment variables.');
  }
  try {
    // Email to Buyer
    console.log('[EmailController] Sending confirmation to buyer:', buyerEmail);
    try {
      await resend.emails.send({
        from: '<EMAIL>',
        to: buyerEmail,
        subject: 'Purchase Confirmation - Thank you for your order!',
        html: `
          <h2>Purchase Confirmation</h2>
          <p>Dear Customer,</p>
          <p>Thank you for your purchase! Your order for <b>${orderData.brandName || orderData.title}</b> has been received and is being processed.</p>
          <p><b>Order Details:</b></p>
          <ul>
            <li>Order ID: ${orderData.orderId || orderData.id}</li>
            <li>Quantity: ${orderData.quantity}</li>
            <li>Total: ${orderData.price || orderData.formattedTotal}</li>
          </ul>
          <p>We will notify you when your item ships.</p>
          <p>Thank you for shopping with us!</p>
        `
      });
      console.log('[EmailController] Buyer confirmation sent:', buyerEmail);
    } catch (buyerErr) {
      console.error('[EmailController] Failed to send buyer confirmation:', buyerEmail, buyerErr);
    }

    // Email to Seller
    console.log('[EmailController] Sending confirmation to seller:', sellerEmail);
    try {
      await resend.emails.send({
        from: '<EMAIL>',
        to: sellerEmail,
        subject: 'Item Sold - New Order Received',
        html: `
          <h2>Item Sold!</h2>
          <p>Congratulations, your item has been sold.</p>
          <p><b>Order Details:</b></p>
          <ul>
            <li>Item: <b>${orderData.brandName || orderData.title || 'N/A'}</b></li>
            <li>Order ID: ${orderData.adId || orderData.id || 'N/A'}</li>
            <li>Quantity Sold: ${orderData.quantity || 'N/A'}</li>
            <li>Buyer Name: ${orderData.buyerDisplayName || orderData.buyerName || 'N/A'}</li>
            <li>Buyer Email: ${orderData.buyerEmail || 'N/A'}</li>
            <li>Ship To: ${orderData.shippingAddress || orderData.shippingLocation || orderData.buyerShippingAddress || 'N/A'}</li>
          </ul>
          <p>Please prepare the item for shipping and coordinate with the buyer if needed.</p>
          <p>Thank you for using our marketplace!</p>
        `
      });
      console.log('[EmailController] Seller confirmation sent:', sellerEmail);
    } catch (sellerErr) {
      console.error('[EmailController] Failed to send seller confirmation:', sellerEmail, sellerErr);
    }

    return { success: true };
  } catch (error) {
    console.error('Error sending confirmation emails:', error);
    return { success: false, message: error.message };
  }
}; 
const { stripe } = require("../../config/stripe");
const { database } = require('../../config/firebase');
const { updateQuantityAfterPayment } = require("./DirectQuantityUpdater");
const { sendOrderConfirmation } = require('../email/EmailController');

/**
 * Create a payment intent for Stripe checkout
 */
exports.createPaymentIntent = async (req, res) => {
  try {
    const { items, currency, userId } = req.body;
    // Always use EUR as the currency (hardcoded for backend)
    const paymentCurrency = "eur"; // lowercase for Stripe API

    if (!items || !items.length) {
      return res.status(400).json({
        success: false,
        message: "No items provided for checkout",
      });
    }

    // Allow test user ID for development
    let userIdToUse = userId;
    if (!userIdToUse) {
      console.warn(
        "No user ID provided for payment intent creation, using test user"
      );
      userIdToUse = "test-user-123";
    }

    // Calculate the total amount from the items
    const amount = items.reduce((total, item) => {
      const price = parseFloat(item.finalPrice || item.price) || 0;
      return total + price * (item.quantity || 1);
    }, 0);

    // Convert to cents for Stripe
    const amountInCents = Math.round(amount * 100);

    // Create a payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amountInCents,
      currency: paymentCurrency,
      metadata: {
        userId: userIdToUse,
        items: JSON.stringify(
          items.map((item) => ({
            adId: item.adId,
            title: item.title,
            quantity: item.quantity || 1,
          }))
        ),
      },
    });

    // Return the client secret to the frontend
    res.status(200).json({
      success: true,
      clientSecret: paymentIntent.client_secret,
    });
  } catch (error) {
    console.error("Error creating payment intent:", error);
    res.status(500).json({
      success: false,
      message: error.message || "Failed to create payment intent",
    });
  }
};

/**
 * Process a successful payment and update orders
 */
exports.processPayment = async (req, res) => {
  try {
    console.log("Processing payment with request body:", req.body);
    const { paymentIntentId, userId, items } = req.body;

    if (!paymentIntentId || !userId || !items || !items.length) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields",
      });
    }

    // Calculate total amount from items
    const amount = items.reduce((total, item) => {
      const price = parseFloat(item.finalPrice || item.price) || 0;
      return total + price * (item.quantity || 1);
    }, 0);

    // Get the payment intent to verify it
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
    
    if (!paymentIntent) {
      return res.status(400).json({
        success: false,
        message: "Payment intent not found",
      });
    }

    // Check if payment was successful
    if (paymentIntent.status === "succeeded" || paymentIntent.status === "processing") {
      console.log("Payment successful or processing");

      // Save order to Firebase and move items to sold status
      const orderPromises = items.map(async (item) => {
        try {
          console.log(`Processing item ${item.adId}`);

          // Get the ad from the seller's ads collection
          let adData = null;
          let sellerId = item.userId;
          const sellerAdSnapshot = await database.ref(`users/${sellerId}/ads/${item.adId}`).once("value");
          adData = sellerAdSnapshot.val();
          if (!adData) {
            throw new Error(`Ad ${item.adId} not found in seller's ads collection`);
          }

          // Fetch buyer info from Firebase
          const buyerSnapshot = await database.ref(`users/${userId}`).once("value");
          const buyerData = buyerSnapshot.val();
          const buyerDisplayName = buyerData?.displayName || '';
          const buyerEmail = buyerData?.email || 'unknown';

          // Fetch shipping info (if you store it under users/{userId}/location or similar)
          const buyerLocationSnapshot = await database.ref(`users/${userId}/location`).once("value");
          const buyerLocation = buyerLocationSnapshot.val();
          const shippingAddress = buyerLocation?.formattedLocation || buyerLocation?.address || '';

          // Fetch seller info from Firebase
          const sellerSnapshot = await database.ref(`users/${sellerId}`).once("value");
          const sellerData = sellerSnapshot.val();
          const sellerEmail = sellerData?.email || adData.userEmail || 'unknown';
          const sellerDisplayName = sellerData?.displayName || adData.userName || '';

          // Create order in buyer's orders collection
          const orderRef = database.ref(`users/${userId}/orders`).push();
          const currentTime = Date.now();
          const orderData = {
            ...adData,
            ...item,
            quantity: item.quantity || 1,
            quantityPurchased: item.quantity || 1,
            buyerDisplayName,
            buyerEmail,
            shippingAddress,
            sellerEmail,
            sellerDisplayName,
            orderStatus: "paid",
            paymentIntentId: paymentIntent.id,
            paymentStatus: "completed",
            // Timestamps
            createdAt: currentTime,
            purchasedAt: currentTime,
            paidAt: currentTime,
            orderDate: currentTime,
            timestamps: {
              created: currentTime,
              paid: currentTime,
              dispatched: null,
              delivered: null
            },
            sellerId: sellerId,
            buyerId: userId,
          };
          await orderRef.set(orderData);
          console.log(`Order saved to buyer's orders with ID: ${orderRef.key}`);

          // Create soldItem in seller's soldItems collection
          const soldItemData = {
            ...adData,
            ...item,
            soldAt: currentTime,
            purchasedAt: currentTime,
            createdAt: currentTime,
            orderDate: currentTime,
            soldTo: userId,
            orderId: orderRef.key,
            quantitySold: item.quantity || 1,
            orderStatus: "paid",
            paymentStatus: "completed",
            price: item.finalPrice || item.price,
            finalPrice: item.finalPrice || item.price,
            originalPrice: item.price,
            discountApplied: item.finalPrice ? (item.price - item.finalPrice) : 0,
            // Timestamps
            timestamps: {
              created: currentTime,
              paid: currentTime,
              dispatched: null,
              delivered: null
            },
            buyerDisplayName,
            buyerEmail,
            shippingAddress,
            sellerEmail,
            sellerDisplayName,
            sellerId: sellerId,
            buyerId: userId,
            buyerInfo: {
              displayName: buyerDisplayName,
              email: buyerEmail,
              shippingAddress: shippingAddress
            }
          };

          console.log('Creating sold item with data:', soldItemData);
          await database.ref(`users/${sellerId}/soldItems/${orderRef.key}`).set(soldItemData);
          console.log(`SoldItem saved to seller's soldItems with ID: ${orderRef.key}`);

          // Update the ad's quantity and status in seller's ads collection
          const sellerAdRef = database.ref(`users/${sellerId}/ads/${item.adId}`);
          const purchaseQuantity = item.quantity || 1;
          const newQuantity = Math.max(0, (adData.quantity || 0) - purchaseQuantity);
          const newQuantityInStock = Math.max(0, (adData.quantityInStock || 0) - purchaseQuantity);
          const newStockStatus = newQuantityInStock > 0 ? "In Stock" : "Out of Stock";
          
          // Keep the original status instead of marking as sold
          await sellerAdRef.update({
            quantity: newQuantity,
            quantityInStock: newQuantityInStock,
            stockStatus: newStockStatus,
            lastPurchaseInfo: {
              soldAt: Date.now(),
              soldTo: userId,
              soldToEmail: buyerEmail,
              orderId: orderRef.key,
              quantityPurchased: purchaseQuantity
            }
          });

          // Update seller's publishedAds with the same approach
          await database.ref(`users/${sellerId}/publishedAds/${item.adId}`).update({
            quantity: newQuantity,
            quantityInStock: newQuantityInStock,
            stockStatus: newStockStatus,
            lastPurchaseInfo: {
              soldAt: Date.now(),
              soldTo: userId,
              soldToEmail: buyerEmail,
              orderId: orderRef.key,
              quantityPurchased: purchaseQuantity
            }
          });

          // Send confirmation emails
          console.log('[processPayment] Sending confirmation emails with:', {
            buyerEmail: orderData.buyerEmail,
            sellerEmail: orderData.sellerEmail,
            buyerDisplayName: orderData.buyerDisplayName,
            sellerDisplayName: orderData.sellerDisplayName,
            shippingAddress: orderData.shippingAddress,
            orderId: orderData.orderId || orderData.id,
            sellerId: orderData.sellerId,
          });

          try {
            await sendOrderConfirmation(
              orderData.buyerEmail,
              orderData.sellerEmail,
              orderData
            );
            console.log('[processPayment] Confirmation emails sent to buyer and seller.');
          } catch (emailError) {
            console.error('[processPayment] Failed to send confirmation emails:', emailError);
          }

          return {
            userOrderId: orderRef.key
          };
        } catch (error) {
          console.error(`Error processing item ${item.adId}:`, error);
          return null;
        }
      });

      const orderResults = await Promise.all(orderPromises);
      const successfulOrders = orderResults.filter(result => result !== null);

      if (successfulOrders.length === 0) {
        return res.status(400).json({
          success: false,
          message: "Failed to process any items",
        });
      }

      res.status(200).json({
        success: true,
        message: "Payment processed successfully",
        orderIds: successfulOrders.map(order => order.userOrderId)
      });
    } else {
      console.log("Payment failed with status:", paymentIntent.status);
      res.status(400).json({
        success: false,
        message: `Payment failed with status: ${paymentIntent.status}`,
      });
    }
  } catch (error) {
    console.error("Error processing payment:", error);
    res.status(500).json({
      success: false,
      message: error.message || "Failed to process payment",
    });
  }
};

/**
 * Handle Stripe webhook events
 */
exports.handleWebhook = async (req, res) => {
  const sig = req.headers["stripe-signature"];
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

  let event;

  try {
    // Verify the webhook signature
    if (webhookSecret) {
      event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
    } else {
      // If no webhook secret is configured, just parse the event
      event = req.body;
    }

    // Handle different event types
    switch (event.type) {
      case "payment_intent.succeeded":
        const paymentIntent = event.data.object;
        console.log("Payment succeeded:", paymentIntent.id);

        // Extract metadata
        const { userId, items } = paymentIntent.metadata;

        if (userId && items) {
          const parsedItems = JSON.parse(items);

          // Update order status in Firebase and update item quantities
          parsedItems.forEach(async (itemData) => {
            console.log(`Webhook: Processing item ${itemData.adId}`);

            // Find orders with this payment intent
            const ordersSnapshot = await database
              .ref(`users/${userId}/orders`)
              .orderByChild("paymentIntentId")
              .equalTo(paymentIntent.id)
              .once("value");

            const orders = ordersSnapshot.val();

            try {
              console.log(
                `WEBHOOK: Processing payment for item ${itemData.adId}`
              );

              // Get the seller's user ID from the item or from the ad in the database
              let sellerId = itemData.userId;

              // If seller ID is not in the item, try to get it from the database
              if (!sellerId) {
                try {
                  const adSnapshot = await database
                    .ref(`users/${userId}/ads/${itemData.adId}`)
                    .once("value");
                  const adData = adSnapshot.val();
                  if (adData && adData.userId) {
                    sellerId = adData.userId;
                    console.log(
                      `WEBHOOK: Found seller ID ${sellerId} for item ${itemData.adId}`
                    );
                  }
                } catch (error) {
                  console.error(
                    `WEBHOOK: Error getting seller ID for item ${itemData.adId}:`,
                    error
                  );
                }
              }

              // Update the ad status to sold but keep it in published ads
              if (sellerId) {
                try {
                  const sellerAdSnapshot = await database.ref(`users/${sellerId}/ads/${itemData.adId}`).once("value");
                  const adData = sellerAdSnapshot.val();
                  
                  if (adData) {
                    const purchaseQuantity = itemData.quantity || 1;
                    const newQuantity = Math.max(0, (adData.quantity || 0) - purchaseQuantity);
                    const newQuantityInStock = Math.max(0, (adData.quantityInStock || 0) - purchaseQuantity);
                    const newStockStatus = newQuantityInStock > 0 ? "In Stock" : "Out of Stock";
                    
                    // Only mark as sold if quantity reaches zero
                    const newStatus = newQuantityInStock === 0 ? "sold" : adData.status;
                    
                    // Update seller's ads
                    await database.ref(`users/${sellerId}/ads/${itemData.adId}`).update({
                      quantity: newQuantity,
                      quantityInStock: newQuantityInStock,
                      stockStatus: newStockStatus,
                      status: newStatus,
                      ...(newStatus === "sold" ? {
                        soldAt: Date.now(),
                        soldTo: userId,
                        orderStatus: "paid",
                        paymentStatus: "succeeded"
                      } : {})
                    });

                    // Update seller's publishedAds
                    await database.ref(`users/${sellerId}/publishedAds/${itemData.adId}`).update({
                      quantity: newQuantity,
                      quantityInStock: newQuantityInStock,
                      stockStatus: newStockStatus,
                      status: newStatus,
                      ...(newStatus === "sold" ? {
                        soldAt: Date.now(),
                        soldTo: userId,
                        orderStatus: "paid",
                        paymentStatus: "succeeded"
                      } : {})
                    });

                    console.log(`WEBHOOK: Updated ad status for item ${itemData.adId}. New status: ${newStatus}, New quantity: ${newQuantityInStock}`);

                    // If sold, create soldItems record
                    if (newStatus === "sold") {
                      console.log("[webhook] Attempting to write to soldItems for sellerId:", sellerId, "adId:", itemData.adId);
                      try {
                        await database.ref(`users/${sellerId}/soldItems/${itemData.adId}`).set({
                          ...adData,
                          ...itemData,
                          status: "sold",
                          soldAt: Date.now(),
                          soldTo: userId,
                          orderStatus: "paid",
                          paymentStatus: "succeeded"
                        });
                        console.log("[webhook] Successfully wrote to soldItems for sellerId:", sellerId, "adId:", itemData.adId);
                      } catch (err) {
                        console.error("[webhook] Failed to write to soldItems:", err);
                      }
                    }
                  }
                } catch (error) {
                  console.error(`WEBHOOK: Error updating ad status: ${error.message}`);
                }
              }

              // Directly update the quantity using our dedicated function
              const itemQuantity = itemData.quantity || 1;
              console.log(
                `WEBHOOK: Processing payment for ${itemQuantity} units of item ${itemData.adId}`
              );

              const updateResult = await updateQuantityAfterPayment(
                itemData.adId,
                userId,
                sellerId,
                itemQuantity
              );

              if (updateResult.success) {
                console.log(
                  `WEBHOOK: Successfully updated quantity for item ${itemData.adId}`
                );
                console.log(
                  `WEBHOOK: New quantity: ${updateResult.newQuantity}, New stock status: ${updateResult.newStockStatus}`
                );
              } else {
                console.error(
                  `WEBHOOK: Failed to update quantity for item ${itemData.adId}: ${updateResult.message}`
                );

                // If the item is out of stock, update the order to reflect this
                if (
                  updateResult.message &&
                  updateResult.message.includes("out of stock")
                ) {
                  console.log(
                    `WEBHOOK: Item ${itemData.adId} is out of stock, updating order status`
                  );

                  // Find the order and update it
                  if (orders) {
                    Object.keys(orders).forEach(async (orderId) => {
                      try {
                        await database
                          .ref(`users/${userId}/orders/${orderId}`)
                          .update({
                            outOfStockAtPurchase: true,
                            stockStatus: "Out of Stock",
                            quantityInStock: 0,
                            quantity: 0,
                          });

                        console.log(
                          `WEBHOOK: Updated order status for out-of-stock item ${itemData.adId}`
                        );
                      } catch (orderUpdateError) {
                        console.error(
                          `WEBHOOK: Error updating order status: ${orderUpdateError.message}`
                        );
                      }
                    });
                  }
                }
              }
            } catch (error) {
              console.error(
                `WEBHOOK: Error processing item ${itemData.adId}:`,
                error
              );
            }

            if (orders) {
              // Update each order status
              Object.keys(orders).forEach(async (orderId) => {
                // Update user's order
                await database
                  .ref(`users/${userId}/orders/${orderId}`)
                  .update({ 
                    orderStatus: "paid",
                    paymentStatus: "completed",
                    paidAt: Date.now(),
                    'timestamps.paid': Date.now()
                  });

                // Also update the seller's soldItems
                const orderSnapshot = await database.ref(`users/${userId}/orders/${orderId}`).once('value');
                const orderData = orderSnapshot.val();
                if (orderData && orderData.sellerId) {
                  await database
                    .ref(`users/${orderData.sellerId}/soldItems/${orderId}`)
                    .update({
                      orderStatus: "paid",
                      paymentStatus: "completed",
                      paidAt: Date.now(),
                      'timestamps.paid': Date.now()
                    });
                }
              });
            }
          });
        }
        break;

      case "payment_intent.payment_failed":
        const failedPaymentIntent = event.data.object;
        console.log("Payment failed:", failedPaymentIntent.id);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.status(200).json({ received: true });
  } catch (error) {
    console.error("Webhook error:", error);
    res.status(400).send(`Webhook Error: ${error.message}`);
  }
};

/**
 * Get Stripe publishable key
 */
exports.getPublishableKey = (_, res) => {
  const { publishableKey } = require("../../config/stripe");

  if (!publishableKey) {
    return res.status(500).json({
      success: false,
      message: "Stripe publishable key not configured",
    });
  }

  res.status(200).json({
    success: true,
    publishableKey,
  });
};

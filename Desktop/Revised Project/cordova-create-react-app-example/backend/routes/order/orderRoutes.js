const express = require('express');
const router = express.Router();
const OrderController = require('../../controllers/order/OrderController');
const { auth } = require('../../middleware/auth');

// Get orders
router.get('/buyer', auth, OrderController.getBuyerOrders);
router.get('/seller', auth, OrderController.getSellerOrders);

// Get single order
router.get('/:orderId', auth, OrderController.getOrder);

// Get order status
router.get('/:orderId/status', auth, OrderController.getOrderStatus);

// Dispatch order
router.post('/:orderId/dispatch', auth, OrderController.dispatchOrder);

// Confirm delivery
router.post('/:orderId/confirm-delivery', auth, OrderController.confirmDelivery);

module.exports = router; 
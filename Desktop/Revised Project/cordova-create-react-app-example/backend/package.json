{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.6.7", "body-parser": "^1.20.3", "cloudinary": "^2.6.0", "compressorjs": "^1.2.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-session": "^1.18.1", "firebase": "^8.10.1", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "node-localstorage": "^3.0.5", "nodemailer": "^6.10.0", "resend": "^4.5.1", "sessionstorage-for-nodejs": "^1.0.2", "streamifier": "^0.1.1", "stripe": "^18.0.0"}, "devDependencies": {"dotenv-cli": "^8.0.0"}}
const firebase = require('firebase/app');
require('firebase/database');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables from .env.firebase
dotenv.config({ path: path.resolve(__dirname, '../.env.firebase') });

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.FIREBASE_API_KEY,
  authDomain: process.env.FIREBASE_AUTH_DOMAIN,
  databaseURL: process.env.FIREBASE_DATABASE_URL,
  projectId: process.env.FIREBASE_PROJECT_ID,
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.FIREBASE_APP_ID,
};

// Initialize Firebase
if (!firebase.apps.length) {
  firebase.initializeApp(firebaseConfig);
}

// Get database instance
const database = firebase.database();

module.exports = {
  firebase,
  database
};

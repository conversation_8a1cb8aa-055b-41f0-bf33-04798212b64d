{"name": "html-to-text", "version": "9.0.5", "description": "Advanced html to plain text converter", "keywords": ["html", "node", "text", "mail", "plain", "converter"], "license": "MIT", "author": "Malte Legenhausen <<EMAIL>>", "contributors": ["KillyMXI <<EMAIL>>"], "homepage": "https://github.com/html-to-text/node-html-to-text", "repository": {"type": "git", "url": "git://github.com/html-to-text/node-html-to-text.git"}, "bugs": {"url": "https://github.com/html-to-text/node-html-to-text/issues"}, "type": "module", "main": "./lib/html-to-text.cjs", "module": "./lib/html-to-text.mjs", "exports": {"import": "./lib/html-to-text.mjs", "require": "./lib/html-to-text.cjs"}, "files": ["lib", "README.md", "CHANGELOG.md", "LICENSE"], "engines": {"node": ">=14"}, "scripts": {"build:rollup": "rollup -c", "build": "npm run clean && npm run build:rollup && npm run copy:license", "clean": "<PERSON><PERSON><PERSON> lib", "copy:license": "copyfiles -f ../../LICENSE .", "cover": "c8 --reporter=lcov --reporter=text-summary mocha -t 20000", "test": "mocha"}, "dependencies": {"@selderee/plugin-htmlparser2": "^0.11.0", "deepmerge": "^4.3.1", "dom-serializer": "^2.0.0", "htmlparser2": "^8.0.2", "selderee": "^0.11.0"}, "mocha": {"node-option": ["experimental-specifier-resolution=node"]}}
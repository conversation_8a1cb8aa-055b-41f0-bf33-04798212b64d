{"version": 3, "file": "encode.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/entities/61afd4701eaa736978b13c7351cd3de9a96b04bc/src/", "sources": ["encode.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,4BAA4B,CAAC;AAClD,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAExD,MAAM,YAAY,GAAG,qCAAqC,CAAC;AAE3D;;;;;;;;;;GAUG;AACH,MAAM,UAAU,UAAU,CAAC,IAAY;IACnC,OAAO,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AAChD,CAAC;AACD;;;;;;;GAOG;AACH,MAAM,UAAU,kBAAkB,CAAC,IAAY;IAC3C,OAAO,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,gBAAgB,CAAC,MAAc,EAAE,GAAW;IACjD,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,KAAK,CAAC;IAEV,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;QACxC,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;QACtB,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QACjC,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAE9B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC1B,kDAAkD;YAClD,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE;gBACpB,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACvC,MAAM,KAAK,GACP,OAAO,IAAI,CAAC,CAAC,KAAK,QAAQ;oBACtB,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,QAAQ;wBACjB,CAAC,CAAC,IAAI,CAAC,CAAC;wBACR,CAAC,CAAC,SAAS;oBACf,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAE/B,IAAI,KAAK,KAAK,SAAS,EAAE;oBACrB,GAAG,IAAI,KAAK,CAAC;oBACb,OAAO,GAAG,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC;oBAChC,SAAS;iBACZ;aACJ;YAED,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;SACjB;QAED,4EAA4E;QAC5E,IAAI,IAAI,KAAK,SAAS,EAAE;YACpB,GAAG,IAAI,IAAI,CAAC;YACZ,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;SACnB;aAAM;YACH,MAAM,EAAE,GAAG,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAChC,GAAG,IAAI,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC;YAChC,4CAA4C;YAC5C,OAAO,GAAG,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC;SACrD;KACJ;IAED,OAAO,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACrC,CAAC"}
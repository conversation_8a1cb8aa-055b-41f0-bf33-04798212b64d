import { BuilderFunction } from './Types';
/**
 * CSS selectors decision tree.
 * Data structure that weaves similar selectors together
 * in order to minimize the number of checks required
 * to find the ones matching a given HTML element.
 *
 * Converted into a functioning implementation via plugins
 * tailored for specific DOM ASTs.
 *
 * @typeParam V - the type of values associated with selectors.
 */
export declare class DecisionTree<V> {
    private readonly branches;
    /**
     * Create new DecisionTree object.
     *
     * @param input - an array containing all selectors
     * paired with associated values.
     *
     * @typeParam V - the type of values associated with selectors.
     */
    constructor(input: [string, V][]);
    /**
     * Turn this decision tree into a usable form.
     *
     * @typeParam R - return type defined by the builder function.
     *
     * @param builder - the builder function.
     *
     * @returns the decision tree in a form ready for use.
     */
    build<R>(builder: BuilderFunction<V, R>): R;
}

import { Element } from 'domhandler';
import { <PERSON><PERSON>, As<PERSON> } from 'selderee';
/**
 * A {@link BuilderFunction} implementation.
 *
 * Creates a function (in a {@link Picker} wrapper) that can run
 * the decision tree against `htmlparser2` `Element` nodes.
 *
 * @typeParam V - the type of values associated with selectors.
 *
 * @param nodes - nodes ({@link DecisionTreeNode})
 * from the root level of the decision tree.
 *
 * @returns a {@link Picker} object.
 */
export declare function hp2Builder<V>(nodes: Ast.DecisionTreeNode<V>[]): Picker<Element, V>;

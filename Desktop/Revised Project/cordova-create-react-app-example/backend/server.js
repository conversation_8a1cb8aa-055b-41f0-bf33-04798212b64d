const express = require("express");
const cors = require("cors");
const bodyParser = require("body-parser");
const dotenv = require("dotenv");
const path = require("path");

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, ".env.firebase") });
dotenv.config({ path: path.resolve(__dirname, ".env.cloudinary") });
dotenv.config({ path: path.resolve(__dirname, ".env.stripe") });
// Load Resend API key from .env.resend
dotenv.config({ path: '.env.resend' });

// Import routes
const authRoutes = require("./routes/authRoutes");
const uploadRoutes = require("./routes/uploadRoutes");
const adRoutes = require("./routes/adRoutes");
const adminRoutes = require("./routes/adminRoutes");
const categoryRoutes = require("./routes/categoryRoutes");
const paymentRoutes = require("./routes/paymentRoutes");
const inventoryRoutes = require("./routes/inventoryRoutes");
const testRoutes = require("./routes/testRoutes");
const directUpdateRoutes = require("./routes/directUpdateRoutes");
const configRoutes = require("./routes/configRoutes");
const orderRoutes = require("./routes/order/orderRoutes");

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 5000;

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, "public")));

// Middleware
app.use(
  cors({
    // Allow multiple origins for credentials
    origin: function (origin, callback) {
      const allowedOrigins = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:3002",
        "http://localhost:5000",
      ];
      // Allow requests with no origin (like mobile apps or curl requests)
      if (!origin || allowedOrigins.indexOf(origin) !== -1) {
        callback(null, true);
      } else {
        console.log("Origin not allowed by CORS:", origin);
        callback(null, true); // Allow all origins for now to debug
      }
    },
    methods: ["GET", "POST", "PUT", "DELETE"],
    allowedHeaders: ["Content-Type", "Authorization", "Accept"],
    credentials: true,
  })
);
// Parse JSON for all routes except Stripe webhook
app.use((req, res, next) => {
  if (req.originalUrl === "/api/payment/webhook") {
    next();
  } else {
    bodyParser.json()(req, res, next);
  }
});
app.use(bodyParser.urlencoded({ extended: true }));

// Initialize Firebase (this is now done in config/firebase.js)
require("./config/firebase");

// Log all incoming requests
app.use((req, res, next) => {
  console.log(`${req.method} ${req.url}`);
  next();
});

// Routes
app.use("/api", authRoutes);
app.use("/api/upload", uploadRoutes);
app.use("/api/ads", adRoutes);
app.use("/api/admin", adminRoutes);
app.use("/api/categories", categoryRoutes);
app.use("/api/payment", paymentRoutes);
app.use("/api/inventory", inventoryRoutes);
app.use("/api/test", testRoutes);
app.use("/api", directUpdateRoutes);
app.use("/api", configRoutes);
app.use("/api/orders", orderRoutes);

// Admin verification endpoint with /api prefix
app.post("/api/admin-verify", (req, res) => {
  console.log("Admin verification endpoint accessed with /api prefix");
  console.log("Request body:", req.body);

  const { email } = req.body;

  if (!email) {
    return res.status(400).json({
      success: false,
      message: "Email is required",
    });
  }

  // List of admin email addresses
  const ADMIN_EMAILS = [
    "<EMAIL>", // Your email
    // Add more admin emails as needed
  ];

  // Check if the email is in the admin list
  const isAdmin = ADMIN_EMAILS.includes(email);

  if (isAdmin) {
    console.log(`Admin verification successful for ${email}`);
    return res.status(200).json({
      success: true,
      message: "Admin verification successful",
    });
  } else {
    console.log(`Admin verification failed for ${email}`);
    return res.status(403).json({
      success: false,
      message: "You do not have admin privileges",
    });
  }
});

// Debug route to test if server is running
app.get("/debug", (req, res) => {
  console.log("Debug route accessed");
  res.json({
    message: "Server is running!",
    routes: {
      apiAds: "/api/ads",
      apiUpload: "/api/upload",
      apiAdmin: "/api/admin",
      apiAdminVerify: "/api/admin-verify",
      apiCategories: "/api/categories",
      apiTest: "/api/test",
    },
  });
});

// In-memory storage for categories
let testCategories = [
  {
    id: "1",
    name: "Electronics",
    parentId: null,
    createdAt: new Date().toISOString(),
    createdBy: "admin",
  },
  {
    id: "2",
    name: "Clothing",
    parentId: null,
    createdAt: new Date().toISOString(),
    createdBy: "admin",
  },
  {
    id: "3",
    name: "Smartphones",
    parentId: "1",
    createdAt: new Date().toISOString(),
    createdBy: "admin",
  },
  {
    id: "4",
    name: "Laptops",
    parentId: "1",
    createdAt: new Date().toISOString(),
    createdBy: "admin",
  },
];

// Test categories route that doesn't use Firebase directly
app.get("/api/test-categories", (req, res) => {
  res.json({
    success: true,
    categories: testCategories,
  });
});

// Add a new category
app.post("/api/test-categories", (req, res) => {
  try {
    const { name, parentId, userId } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: "Category name is required",
      });
    }

    const newCategory = {
      id: Date.now().toString(),
      name,
      parentId: parentId || null,
      createdAt: new Date().toISOString(),
      createdBy: userId || "admin",
    };

    testCategories.push(newCategory);

    return res.status(201).json({
      success: true,
      message: "Category added successfully",
      categoryId: newCategory.id,
    });
  } catch (error) {
    console.error("Error adding category:", error);
    return res.status(500).json({
      success: false,
      message: "Error adding category",
      error: error.message,
    });
  }
});

// Update a category
app.put("/api/test-categories/:id", (req, res) => {
  try {
    const { id } = req.params;
    const { name, userId } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: "Category name is required",
      });
    }

    const categoryIndex = testCategories.findIndex((cat) => cat.id === id);

    if (categoryIndex === -1) {
      return res.status(404).json({
        success: false,
        message: "Category not found",
      });
    }

    testCategories[categoryIndex] = {
      ...testCategories[categoryIndex],
      name,
      updatedAt: new Date().toISOString(),
      updatedBy: userId || "admin",
    };

    return res.status(200).json({
      success: true,
      message: "Category updated successfully",
    });
  } catch (error) {
    console.error("Error updating category:", error);
    return res.status(500).json({
      success: false,
      message: "Error updating category",
      error: error.message,
    });
  }
});

// Delete a category
app.delete("/api/test-categories/:id", (req, res) => {
  try {
    const { id } = req.params;

    // Check if category exists
    const categoryIndex = testCategories.findIndex((cat) => cat.id === id);

    if (categoryIndex === -1) {
      return res.status(404).json({
        success: false,
        message: "Category not found",
      });
    }

    // Check if there are any subcategories
    const hasSubcategories = testCategories.some((cat) => cat.parentId === id);

    if (hasSubcategories) {
      return res.status(400).json({
        success: false,
        message: "Cannot delete category with subcategories",
      });
    }

    // Delete the category
    testCategories.splice(categoryIndex, 1);

    return res.status(200).json({
      success: true,
      message: "Category deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting category:", error);
    return res.status(500).json({
      success: false,
      message: "Error deleting category",
      error: error.message,
    });
  }
});

// Get subcategories for a parent category
app.get("/api/test-categories/subcategories/:parentId", (req, res) => {
  try {
    const { parentId } = req.params;

    const subcategories = testCategories.filter(
      (cat) => cat.parentId === parentId
    );

    return res.status(200).json({
      success: true,
      subcategories,
    });
  } catch (error) {
    console.error("Error getting subcategories:", error);
    return res.status(500).json({
      success: false,
      message: "Error getting subcategories",
      error: error.message,
    });
  }
});

// Test route
app.get("/api/test", (req, res) => {
  res.json({ message: "Server is running!" });
});

// Direct admin verification endpoint (without /api prefix)
app.post("/admin-verify", (req, res) => {
  console.log("Direct admin verification endpoint accessed");
  console.log("Request body:", req.body);

  const { email } = req.body;

  if (!email) {
    return res.status(400).json({
      success: false,
      message: "Email is required",
    });
  }

  // List of admin email addresses
  const ADMIN_EMAILS = [
    "<EMAIL>", // Your email
    // Add more admin emails as needed
  ];

  // Check if the email is in the admin list
  const isAdmin = ADMIN_EMAILS.includes(email);

  if (isAdmin) {
    console.log(`Admin verification successful for ${email}`);
    return res.status(200).json({
      success: true,
      message: "Admin verification successful",
    });
  } else {
    console.log(`Admin verification failed for ${email}`);
    return res.status(403).json({
      success: false,
      message: "You do not have admin privileges",
    });
  }
});

// OPTIONS route for admin-verify to handle preflight requests
app.options("/admin-verify", (req, res) => {
  res.status(200).end();
});

// Global error handler (should be the last middleware)
app.use((err, req, res, next) => {
  console.error('Global error handler:', err.stack);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: err.message
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

{"rules": {".read": true, ".write": true, "users": {".read": true, ".write": true, ".indexOn": ["email", "displayName"], "$uid": {".read": true, ".write": true, "publishedAds": {".read": true, ".write": true, ".indexOn": ["createdAt", "status"]}, "ads": {".read": true, ".write": true, ".indexOn": ["createdAt", "status"], "$adId": {".read": true, ".write": true}}, "soldItems": {".read": true, ".write": true, ".indexOn": ["createdAt", "orderStatus"], "$orderId": {".read": true, ".write": true}}, "orders": {".read": true, ".write": true, ".indexOn": ["createdAt", "orderStatus", "adId"], "$orderId": {".read": true, ".write": true}}, "paidOrders": {".read": true, ".write": true, ".indexOn": ["createdAt", "orderStatus"]}, "cart": {".read": true, ".write": true, ".indexOn": ["addedAt"]}, "notifications": {".read": true, ".write": true, ".indexOn": ["createdAt", "read"]}}}, "publishedAds": {".read": true, ".write": true, ".indexOn": ["category", "brandName", "price", "createdAt", "status"]}, "products": {".read": true, ".write": true, ".indexOn": ["category", "createdAt"]}, "orders": {".read": true, ".write": true, ".indexOn": ["buyerId", "sellerId", "orderStatus", "createdAt"]}, "paidOrders": {".read": true, ".write": true, ".indexOn": ["buyerId", "sellerId", "createdAt"]}, "soldItems": {".read": true, ".write": true, ".indexOn": ["sellerId", "orderStatus", "createdAt"]}, "categories": {".read": true, ".write": true, ".indexOn": ["name", "createdAt"]}, "messages": {".read": true, ".write": true, ".indexOn": ["senderId", "receiverId", "createdAt"], "$messageId": {".read": true, ".write": true}}, "chats": {".read": true, ".write": true, ".indexOn": ["participants", "lastMessageAt"], "$chatId": {".read": true, ".write": true, "messages": {".read": true, ".write": true, ".indexOn": ["createdAt"]}}}}}